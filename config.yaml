name: "Space Launch Notifier (DEV)"
version: "2025.08.04.8dd6baeb"
slug: "space-launch-notifier"
description: "Get WhatsApp notifications for upcoming rocket launches with livestreams"
url: "https://github.com/kenibarwick/space-launch-notifier"
arch:
  - aarch64
  - amd64
  - armhf
  - armv7
  - i386
startup: application
logo: "🚀"
icon: "🚀"
boot: auto
init: false
ingress: true
ingress_port: 3030
panel_icon: "🚀"
panel_title: "Space Launch Notifier"
hassio_api: true
homeassistant_api: false
host_network: false
ports:
  3030/tcp: 3030
ports_description:
  3030/tcp: "Dashboard web interface"
map:
  - config:rw
  - share:rw
environment:
  SLN_ENV: DEV
whatsapp:
  sessionName: "launch-notifier"
  connectionRetry:
    maxRetries: 3
    retryDelay: 5000
    initWait: 10000
  puppeteerOptions:
    args:
      - "--no-sandbox"
      - "--disable-setuid-sandbox"
      - "--disable-dev-shm-usage"
      - "--disable-accelerated-2d-canvas"
      - "--disable-gpu"
      - "--window-size=1920x1080"
      - "--disable-web-security"
      - "--ignore-certificate-errors"
      - "--ignore-certificate-errors-spki-list"
      - "--disable-features=IsolateOrigins,site-per-process"
      - "--disable-site-isolation-trials"
      - "--disable-extensions"
      - "--disable-component-extensions-with-background-pages"
      - "--disable-default-apps"
    headless: "new"
    timeout: 60000
whatsappGroupSearch:
  name: "Space Launch Notifier"
api:
  launchApiUrl: "https://ll.thespacedevs.com/2.3.0/launches/upcoming/"
  launchByIdUrl: "https://ll.thespacedevs.com/2.3.0/launches/"
  cacheDuration: 60
  requestTimeout: 30000
  maxResults: 10
  rateLimitMinutes: 4
  maxRetries: 3
  retryDelay: 2000
notifications:
  checkInterval: 1
  defaultTime: 3
  additionalTimes: "60,30,10"
  enableCountdown: true
dailyDigest:
  enabled: true
  time: "09:00"
  lookAheadDays: 1
  includeNoWebcast: true
  includeImage: true
  timezone: "Europe/London"
language:
  default: "en"
  multiLang: false
  languages:
    - "en"
images:
  enabled: true
  useHighRes: true
  preferMissionPatches: true
  fallbackToGeneric: true
  cacheImages: true
  defaultImage: "https://www.nasa.gov/wp-content/uploads/2023/03/nasa-logo-web-rgb.png"
  validationTimeout: 5000
urlTracking:
  enabled: true
  shortenUrls: true
  trackClicks: true
  bitlyApiKey: ""
paths:
  tokensDir: "./tokens"
  dataDir: "./data"
  templatesDir: "./utils/templates"
  logsDir: "./data/logs"
logging:
  level: "debug"
  console:
    enabled: true
    level: "info"
    colorize: true
  file:
    enabled: true
    level: "info"
    maxSize: "20m"
    maxFiles: "14d"
    dirname: "Space Launch Notifier"
debug: true

options:
  whatsapp_group_id: "<EMAIL>" # Placeholder for user input

schema:
  whatsapp_group_id: str
  debug: bool

