// utils/messaging.js - WhatsApp messaging functions

// Import config module without destructuring to avoid redeclaration
module.exports = (config, state) => {

  const venom = require('venom-bot');
  const axios = require('axios');
  const fs = require('fs').promises;
  const path = require('path');
  const os = require('os');
  const languageManager = require('./language-manager');
  const imageProvider = require('./image-provider');
  const progress = require('./progress-indicator');
  const chromeUtils = require('./chrome-utils');
  const moment = require('moment');
  const helpers = require('./helpers');
  const defaultConfig = require('./default-config');
  const i18nLoader = require('./i18n/loader');

  // Import logger
  const logger = require('./logger');
  const log = logger.getLogger('messaging');

  // Import dashboard for message tracking (if available)


  /**
   * Initialize WhatsApp connection
   * @returns {Promise<Object>} WhatsApp client
   */
  async function connectToWhatsApp() {
    log.info('Starting WhatsApp connection...');

    try {
      log.info('Launching browser...');

      // Clean up any existing Chrome sessions before starting
      await chromeUtils.cleanupChromeSession();
      log.info('Cleaned up existing Chrome sessions');

      // Create a custom options object with extended timeouts and improved stability
      const venomOptions = {
        session: config.whatsapp.sessionName,
        multidevice: true,
        headless: 'new',
        debug: config.debug,
        logQR: true,
        disableWelcome: true,
        useChrome: true,
        // Use puppeteer options from configuration with extended timeouts
        puppeteerOptions: {
          ...config.whatsapp.puppeteerOptions,
          // Increase default timeout to 3 minutes
          timeout: 300000,
          // Increase protocol timeout for Puppeteer operations
          protocolTimeout: 1200000,
          // Add additional browser context options for stability
          browserContext: 'default',
          // Reduce memory usage
          defaultViewport: { width: 1280, height: 800 }
        },
        // Add explicit waits
        waitForLogin: true,
        disableSpins: true,
        // Add a custom function to handle the API injection timeout
        waitForFunction: {
          // Increase the timeout for the Store object to 3 minutes
          timeoutMs: 180000
        },
        // Add browser restart options
        restartOnCrash: true,
        // Add browser close options
        browserWS: '',
        browserArgs: config.whatsapp.puppeteerOptions.args
      };

      log.info('Launching WhatsApp with extended timeouts...');

      // Create WhatsApp client with enhanced settings
      const client = await venom.create(venomOptions).catch(err => {
        log.error('Error during venom.create:', err);
        throw err;
      });

      log.info('WhatsApp connection established!');
      state.whatsappClient = client;

      log.info('Waiting for browser to fully initialize...');
      log.info('Initializing browser environment...');
      await new Promise(resolve => setTimeout(resolve, config.whatsapp.connectionRetry.initWait));

      log.info('Setting up message handlers...');
      log.info('Configuring message handlers...');
      client.onMessage(async (message) => {
        try {

          // Skip messages sent by the bot itself
          if (message.fromMe) return;

          // Extract message content
          const sender = message.from;
          const chat = message.from;
          const msgContent = message.body || '';

          // Check if this is a language command
          if (languageManager.processLanguageCommand(message)) {
            // Send confirmation in the user's new language
            const response = languageManager.getCommandResponse(sender, 'languageChanged');
            await client.sendText(chat, response);
            return;
          }

          // Process other commands
          if (msgContent === '/help') {
            const helpMessage = languageManager.getCommandResponse(sender, 'helpMessage');
            await client.sendText(chat, helpMessage);
            log.info('Sent help message');
            return;
          }

          if (msgContent.startsWith('/rate')) {
            const parts = msgContent.split(' ');
            const rating = parts.length > 1 ? parseInt(parts[1]) : 0;

            if (rating >= 1 && rating <= 5) {
              // Record the feedback
              if (typeof feedbackTracker !== 'undefined') {
                feedbackTracker.recordFeedback(sender, 'rating', { rating });
              }
              const response = languageManager.getCommandResponse(sender, 'ratingReceived', { rating });
              await client.sendText(chat, response);
              log.info(`Recorded ${rating}-star rating from ${sender}`);
            } else {
              const response = languageManager.getCommandResponse(sender, 'invalidRating');
              await client.sendText(chat, response);
            }
            return;
          }

          if (msgContent.startsWith('/watched')) {
            const parts = msgContent.split(' ');
            const launchId = parts.length > 1 ? parts[1] : 'unknown';

            if (typeof feedbackTracker !== 'undefined') {
              feedbackTracker.recordFeedback(sender, 'watched', { launchId });
            }
            const response = languageManager.getCommandResponse(sender, 'watchedConfirmation');
            await client.sendText(chat, response);
            log.info(`Recorded watched feedback for launch ${launchId}`);
            return;
          }

          // If no command was recognized, process as a regular message
          // Additional message handling can be added here
        } catch (error) {
          log.error('Error handling message:', error);
        }
      });

      log.info('Message handler set up successfully');

      // Send initial status message using the status template, only if debug mode is enabled
      if (config.debug) {
        log.info(`Sending welcome message to group (v${config.version})...`);
        await sendGroupMessage(
          messageTemplates.formatStatusMessage('startup'),
          { type: 'welcome' }
        );
      } else {
        log.info('Skipping welcome message as debug mode is off.');
      }

      log.info('WhatsApp connection process complete!');
      log.info(`Connected to WhatsApp group: ${config.whatsapp.groupId}`);

      return client;
    } catch (error) {
      // Check if this is a timeout error
      if (error.name === 'TimeoutError' || error.message.includes('timeout') || error.message.includes('Timeout')) {
        log.warn('WhatsApp connection timed out. Attempting recovery...');

        try {
          // Clean up any existing Chrome processes that might be causing issues
          await chromeUtils.cleanupChromeSession();
          log.info('Cleaned up Chrome session files');

          // Try to recover by cleaning up the session directory
          const fs = require('fs');
          const path = require('path');
          const tokenDir = path.join(process.cwd(), config.paths.tokensDir, config.whatsapp.sessionName);

          // Remove the entire session directory to start fresh
          if (fs.existsSync(tokenDir)) {
            log.info('Removing problematic session files...');
            // Only remove specific problematic files, not the entire directory
            const problematicFiles = [
              'SingletonLock', 'SingletonCookie', 'SingletonSocket', 'DevToolsActivePort',
              'Local State', 'Preferences', 'Network Action Predictor', 'Network Persistent State'
            ];

            problematicFiles.forEach(file => {
              const filePath = path.join(tokenDir, file);
              if (fs.existsSync(filePath)) {
                fs.unlinkSync(filePath);
              }
            });

            // Also check the Default directory
            const defaultDir = path.join(tokenDir, 'Default');
            if (fs.existsSync(defaultDir)) {
              ['Cookies', 'Preferences', 'Network Action Predictor', 'Network Persistent State'].forEach(file => {
                const filePath = path.join(defaultDir, file);
                if (fs.existsSync(filePath)) {
                  fs.unlinkSync(filePath);
                }
              });
            }

            log.warn('Session files cleaned. Please restart the application.');
          }

          // Provide helpful error message
          log.error('WhatsApp connection timed out. Please run "npm run restart" to try again.');
          log.error('\nTIMEOUT ERROR: The WhatsApp web page took too long to load.');
          log.error('This is often a temporary issue. Please try the following steps:');
          log.error('1. Run "npm run clean" to remove lock files');
          log.error('2. Run "npm run restart" to restart the application');
          log.error('3. If the issue persists, try "node qr-web.js" to authenticate via browser');
        } catch (cleanupError) {
          log.error('Error during cleanup:', cleanupError);
        }
      } else {
        // For other types of errors
        log.error(`Failed to connect to WhatsApp: ${error.message}`, error);
      }

      log.error('Connection error details:', error);
      throw error;
    }
  }

  /**
   * Check if the Puppeteer page is ready for interactions
   * @param {Object} client - WhatsApp client object
   * @returns {Promise<Boolean>} Whether the page is ready
   */
  async function checkPageReadiness(client) {
    try {
      // Check if client has the page property
      if (!client.page) {
        log.warn('Client page not available yet');
        return false;
      }

      // Try to get the main frame status
      const status = await client.page.evaluate(() => {
        return document.readyState;
      }).catch(() => null);

      return status === 'complete';
    } catch (error) {
      log.error('Error checking page readiness:', error.message);
      return false;
    }
  }

  /**
   * Send a message to the WhatsApp group with automatic retry and format detection
   * @param {String} message - Message text to send
   * @param {Object} metadata - Additional metadata about the message
   * @returns {Promise<Boolean>} Whether the message was sent successfully
   */
  async function sendGroupMessage(message, metadata = {}) {
    log.info('Preparing to send message...');

    if (!state.whatsappClient) {
      log.error('WhatsApp client not initialized');
      return false;
    }

    // Get retry configuration from config
    const maxRetries = config.whatsapp.connectionRetry.maxRetries;
    const retryDelay = config.whatsapp.connectionRetry.retryDelay;
    let retries = 0;

    // Original group ID
    let groupId = config.whatsapp.groupId;

    // Try standard format first with retries
    while (retries < maxRetries) {
      try {
        // Add a longer delay before sending to ensure connection is ready
        log.info(`Preparing connection (${retries + 1}/${maxRetries})...`);
        await new Promise(resolve => setTimeout(resolve, 3000));

        log.info(`Sending message to group: ${groupId} (attempt ${retries + 1}/${maxRetries})`);

        // Check if page is ready before sending
        const isReady = await checkPageReadiness(state.whatsappClient);
        if (!isReady) {
          log.warn("Page not ready yet, waiting longer...");
          await new Promise(resolve => setTimeout(resolve, 5000));
        }

        await state.whatsappClient.sendText(groupId, message);
        log.info('Message sent successfully to group');

        // Record successful message in dashboard
        if (state.dashboard && typeof state.dashboard.recordMessageSent === 'function') {
          state.dashboard.recordMessageSent(true, message);
        }

        return true;
      } catch (firstError) {
        retries++;
        log.warn(`Attempt ${retries}/${maxRetries} failed:`, { error: firstError.message, stack: firstError.stack });

        if (firstError.message && firstError.message.includes('500 Internal Server Error')) {
          log.error('WhatsApp server returned a 500 error. This is likely a temporary issue with WhatsApp.');
        }

        if (retries < maxRetries) {
          // Wait before retry with same format
          log.info(`Waiting ${retryDelay / 1000} seconds before retrying...`);
          await new Promise(resolve => setTimeout(resolve, retryDelay));
          continue;
        }

        log.warn('All standard format attempts failed, trying alternative formats...');

        // Try alternative formats
        try {
          // Try without the @g.us part
          if (groupId.includes('@g.us')) {
            const alternativeId = groupId.split('@')[0];
            log.info(`Trying alternative format: ${alternativeId}`);

            // Add a small delay
            await new Promise(resolve => setTimeout(resolve, 1000));

            await state.whatsappClient.sendText(alternativeId, message);
            log.info('Message sent with alternative ID format');
            // Update config to use this format in the future
            config.whatsapp.groupId = alternativeId;

            // Record successful message in dashboard
            if (state.dashboard && typeof state.dashboard.recordMessageSent === 'function') {
              state.dashboard.recordMessageSent(true, message);
            }

            return true;
          }

          // Try adding @c.us if not present
          if (!groupId.includes('@')) {
            const alternativeId = `${groupId}@c.us`;
            log.info(`Trying alternative format: ${alternativeId}`);

            // Add a small delay
            await new Promise(resolve => setTimeout(resolve, 1000));

            await state.whatsappClient.sendText(alternativeId, message);
            log.info('Message sent with alternative ID format');
            // Update config to use this format in the future
            config.whatsapp.groupId = alternativeId;
            return true;
          }

          throw firstError; // If none of the alternatives worked
        } catch (secondError) {
          log.warn(`All format attempts failed:`, { error: secondError.message, stack: secondError.stack });

          // As a last resort, try to find the group by name
          try {
            log.info(`Attempting to find group by name "${config.whatsappGroupSearch.name}"...`);

            // Add a small delay
            await new Promise(resolve => setTimeout(resolve, 1000));

            const chats = await state.whatsappClient.getAllChats();
            const boomTimeChat = chats.find(chat =>
              chat.name && chat.name.toLowerCase().includes(config.whatsappGroupSearch.name.toLowerCase()));

            if (boomTimeChat) {
              log.info(`Found group by name: ${boomTimeChat.name}, ID: ${boomTimeChat.id._serialized}`);

              // Add a small delay
              await new Promise(resolve => setTimeout(resolve, 1000));

              await state.whatsappClient.sendText(boomTimeChat.id._serialized, message);
              log.info('Message sent by finding group by name');
              // Update config to use this ID in the future
              config.whatsapp.groupId = boomTimeChat.id._serialized;

              // Record successful message in dashboard
              if (state.dashboard && typeof state.dashboard.recordMessageSent === 'function') {
                state.dashboard.recordMessageSent(true, message);
              }

              return true;
            } else {
              throw new Error('Could not find group by name containing "boom"');
            }
          } catch (finalError) {
            log.error(`Final attempt failed:`, { error: finalError.message, stack: finalError.stack });

            // Record failed message in dashboard
            if (state.dashboard && typeof state.dashboard.recordMessageSent === 'function') {
              state.dashboard.recordMessageSent(false);
            }

            return false;
          }
        }
      }
    }

    log.error('Failed to send message after all attempts', { groupId: groupId, message: message, clientInitialized: !!state.whatsappClient, errorDetails: firstError });

    // Record failed message in dashboard
    if (state.dashboard && typeof state.dashboard.recordMessageSent === 'function') {
      state.dashboard.recordMessageSent(false);
    }

    return false;
  }

  /**
   * Download an image from a URL to a local temporary file
   * @param {String} imageUrl - The URL of the image to download
   * @returns {Promise<String|null>} Path to the downloaded file or null if failed
   */
  async function downloadImageToTemp(imageUrl) {
    try {
      log.info(`Downloading image from ${imageUrl}`);

      // Create a temporary file path
      const tempDir = os.tmpdir();
      const tempFilePath = path.join(tempDir, `space_launch_${Date.now()}.jpg`);

      // Download the image with a timeout from config
      const response = await axios({
        method: 'get',
        url: imageUrl,
        responseType: 'arraybuffer',
        timeout: config.api.requestTimeout
      });

      // Check if response is an image
      const contentType = response.headers['content-type'];
      if (!contentType || !contentType.startsWith('image/')) {
        log.warn(`Downloaded content is not an image: ${contentType}`);
        return null;
      }

      // Write the image data to a temp file
      await fs.writeFile(tempFilePath, response.data);
      log.info(`Image downloaded to ${tempFilePath}`);

      return tempFilePath;
    } catch (error) {
      log.error(`Error downloading image from ${imageUrl}:`, error.message);
      return null;
    }
  }

  /**
   * Send an image message to the WhatsApp group
   * @param {String} imageUrl - URL of the image to send
   * @param {String} filename - Filename to use for the image
   * @param {String} caption - Caption to display with the image
   * @returns {Promise<Boolean>} Whether the image was sent successfully
   */
  async function sendGroupImage(imageUrl, filename, caption) {
    if (!state.whatsappClient) {
      log.error('WhatsApp client not initialized');
      return false;
    }

    // First, validate the image URL
    const isValid = await imageProvider.validateImageUrl(imageUrl);
    if (!isValid) {
      log.error(`Invalid or inaccessible image URL: ${imageUrl}`);
      return false;
    }

    // Get retry configuration from config
    const maxRetries = config.whatsapp.connectionRetry.maxRetries;
    const retryDelay = config.whatsapp.connectionRetry.retryDelay;
    let retries = 0;

    // Original group ID
    let groupId = config.whatsapp.groupId;

    // Try standard format first with retries
    while (retries < maxRetries) {
      try {
        // Add a longer delay before sending to ensure connection is ready
        await new Promise(resolve => setTimeout(resolve, 3000));

        log.info(`Attempting to send image to group: ${groupId} (attempt ${retries + 1}/${maxRetries})`);

        // Check if page is ready before sending
        const isReady = await checkPageReadiness(state.whatsappClient);
        if (!isReady) {
          log.warn("Page not ready yet, waiting longer...");
          await new Promise(resolve => setTimeout(resolve, 5000));
        }

        // For some URLs, downloading to a temp file first works better
        let result;
        try {
          // First try sending directly from URL
          result = await state.whatsappClient.sendImage(
            groupId,
            imageUrl,
            filename || 'image.jpg',
            caption || ''
          );
        } catch (directUrlError) {
          log.warn('Failed to send image directly from URL, trying with download:', directUrlError.message);

          // Download image to temporary file first
          const localImagePath = await downloadImageToTemp(imageUrl);
          if (!localImagePath) {
            log.error('Failed to download image to local file, cannot send image.');
            return false; // Return false if image download fails
          }

          // Try sending from local file
          result = await state.whatsappClient.sendImage(
            groupId,
            localImagePath,
            filename || 'image.jpg',
            caption || ''
          );

          // Clean up temp file
          fs.unlink(localImagePath).catch(err => {
            log.warn(`Failed to delete temp file ${localImagePath}:`, err.message);
          });
        }

        log.info('Image sent successfully to group');
        return true;
      } catch (firstError) {
        retries++;
        log.warn(`Image send attempt ${retries}/${maxRetries} failed:`, firstError.message);

        if (retries < maxRetries) {
          // Wait before retry with same format
          log.info(`Waiting ${retryDelay / 1000} seconds before retrying...`);
          await new Promise(resolve => setTimeout(resolve, retryDelay));
          continue;
        }

        // If all retries fail, try alternative group ID formats
        log.warn('All standard format attempts failed, trying alternative formats...');

        // Similar to sendGroupMessage, try alternative formats
        try {
          // Try without the @g.us part
          if (groupId.includes('@g.us')) {
            const alternativeId = groupId.split('@')[0];
            log.info(`Trying alternative format: ${alternativeId}`);

            await state.whatsappClient.sendImage(
              alternativeId,
              imageUrl,
              filename || 'image.jpg',
              caption || ''
            );

            log.info('Image sent with alternative ID format');
            config.whatsapp.groupId = alternativeId;
            return true;
          }

          // Try with @c.us if no @ is present
          if (!groupId.includes('@')) {
            const alternativeId = `${groupId}@c.us`;
            log.info(`Trying alternative format: ${alternativeId}`);

            await state.whatsappClient.sendImage(
              alternativeId,
              imageUrl,
              filename || 'image.jpg',
              caption || ''
            );

            log.info('Image sent with alternative ID format');
            config.whatsapp.groupId = alternativeId;
            return true;
          }

          // Try finding the group by name as a last resort
          log.info('Attempting to find group by name...');
          const chats = await state.whatsappClient.getAllChats();
          const boomTimeChat = chats.find(chat =>
            chat.name && chat.name.toLowerCase().includes('boom'));

          if (boomTimeChat) {
            log.info(`Found group by name: ${boomTimeChat.name}`);

            await state.whatsappClient.sendImage(
              boomTimeChat.id._serialized,
              imageUrl,
              filename || 'image.jpg',
              caption || ''
            );

            log.info('Image sent by finding group by name');
            config.whatsapp.groupId = boomTimeChat.id._serialized;
            return true;
          }

          throw new Error('Failed to send image with all ID formats');

        } catch (finalError) {
          log.error('Failed to send image after all attempts:', finalError.message);
          return false;
        }
      }
    }

    log.error('Failed to send message after all attempts', { groupId: groupId, message: message, clientInitialized: !!state.whatsappClient, errorDetails: finalError });

    // Record failed message in dashboard
    if (state.dashboard && typeof state.dashboard.recordMessageSent === 'function') {
      state.dashboard.recordMessageSent(false);
    }

    return false;
  }

  /**
   * Send a launch notification with an image followed by a text message
   * @param {Object} launch - Launch object from API
   * @returns {Promise<Boolean>} Whether the notification was sent successfully
   */
  async function sendLaunchNotificationWithImage(launch) {
    log.debug(`Attempting to send launch notification for ${launch?.name || 'unknown launch'}`);
    try {
      // Get image for this launch
      const imageUrl = await imageProvider.getLaunchImageUrl(launch);
      log.debug(`Image URL for ${launch?.name || 'unknown launch'}: ${imageUrl}`);

      // Get time information
      const timeInfo = messageTemplates.formatLaunchTime(launch);

      // Format the notification content
      const textMessage = await messageTemplates.formatLaunchNotification(launch);

      // Format caption for the image message
      let caption = `🚀 ${launch.name}`;
      caption += `\nLaunching in ${timeInfo.minutesUntilLaunch} minutes!`;

      if (launch.mission?.name) {
        caption += `\nMission: ${launch.mission.name}`;
      }

      // Variable to track if any message was sent successfully
      let anySent = false;

      // Send image first if available
      if (imageUrl && config.images.enabled) {
        try {
          log.info(`Sending launch image for ${launch.name}: ${imageUrl}`);

          // Generate a filename based on the launch name
          const filename = `${launch.name.replace(/[^a-zA-Z0-9]/g, '_')}.jpg`;

          const imageSent = await sendGroupImage(imageUrl, filename, caption);

          if (imageSent) {
            log.info(`Successfully sent image for ${launch.name}`);
            anySent = true;

            // Small delay between image and text
            await new Promise(resolve => setTimeout(resolve, 1500));
          } else {
            log.error(`Failed to send image for ${launch.name}`);
          }
        } catch (imageError) {
          log.error(`Error sending image for ${launch.name}:`, imageError);
          // Continue to text message even if image fails
        }
      } else {
        log.info(`No image available for ${launch.name} or images disabled`);
      }

      // Send text message
      try {
        const messageSent = await sendGroupMessage(textMessage);

        if (messageSent) {
          log.info(`Successfully sent text notification for ${launch.name}`);
          anySent = true;
        }
        else {
          log.error(`Failed to send text notification for ${launch.name}`);
        }
      } catch (textError) {
        log.error(`Error sending text notification for ${launch.name}:`, textError);
      }

      return anySent;
    } catch (error) {
      log.error(`Error in launch notification process for ${launch?.name || 'unknown launch'}:`, { error: error.message, stack: error.stack });
      return false;
    }
  }



  /**
   * Gracefully close WhatsApp connection
   */
  async function closeWhatsAppConnection() {
    if (state.whatsappClient) {
      try {
        await sendGroupMessage(messageTemplates.formatStatusMessage('shutdown'));
        log.info('Closing WhatsApp client...');
        await state.whatsappClient.close();
        log.info('WhatsApp client closed');
      } catch (error) {
        log.error('Error closing WhatsApp connection:', error);
      }
    }
  }

  /**
   * Send a notification about a launch delay
   * @param {Object} launch - Launch object from API
   * @param {Object} delayInfo - Delay information object
   * @returns {Promise<Boolean>} Whether the notification was sent successfully
   */
  async function sendLaunchDelayNotification(launch, delayInfo) {
    try {
      log.info(`Preparing delay notification for ${launch.name} (delayed by ${delayInfo.delayMinutes} minutes)`);

      // Format the delay notification message
      const message = messageTemplates.formatLaunchDelayNotification(launch, delayInfo);

      // Send the message
      const success = await sendGroupMessage(message);

      if (success) {
        log.info(`Successfully sent delay notification for ${launch.name}`);
        return true;
      } else {
        log.error(`Failed to send delay notification for ${launch.name}`);
        return false;
      }
    } catch (error) {
      log.error(`Error sending delay notification for ${launch.name}:`, error);
      return false;
    }
  }

  return {
    connectToWhatsApp,
    sendGroupMessage,
    sendGroupImage,
    sendLaunchNotificationWithImage,
    sendLaunchDelayNotification,
    closeWhatsAppConnection,
    checkPageReadiness,
    downloadImageToTemp
  };
};