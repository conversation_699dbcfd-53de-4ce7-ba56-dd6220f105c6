// utils/image-provider.js
// Provides images for launches directly from the Launch Library API data

const axios = require('axios');
// Import config module without destructuring to avoid redeclaration
module.exports = (config, state) => {

    // Use the default image from configuration
    const DEFAULT_LAUNCH_IMAGE = config.images.defaultImage;

    /**
     * Validates if an image URL is accessible and returns a valid image
     * @param {String} url - The image URL to validate
     * @returns {Promise<Boolean>} Whether the URL is valid
     */
    async function validateImageUrl(url) {
        if (!url) return false;

        try {
            // Make a HEAD request to check if the image exists
            const response = await axios.head(url, {
                timeout: config.images.validationTimeout,
                validateStatus: function (status) {
                    return status >= 200 && status < 300; // Only accept 2xx status codes
                }
            });

            // Check Content-Type to ensure it's an image
            const contentType = response.headers['content-type'];
            if (contentType && contentType.startsWith('image/')) {
                return true;
            }

            console.warn(`URL exists but is not an image: ${url}, content-type: ${contentType}`);
            return false;
        } catch (error) {
            console.warn(`Invalid or inaccessible image URL: ${url}`, error.message);
            return false;
        }
    }

    /**
     * Extract image fields from the launch object, prioritizing the most relevant ones
     * @param {Object} launch - Launch object from the API
     * @returns {Array<String>} Array of potential image URLs
     */
    function extractLaunchImages(launch) {
        const imageUrls = [];

        // First priority: Mission patch
        if (launch.mission?.image) {
            imageUrls.push(launch.mission.image.image_url);
        }

        // Second priority: Launch image
        if (launch.image?.image_url) {
            imageUrls.push(launch.image.image_url);
        }

        // Third priority: Rocket image
        if (launch.rocket?.configuration?.image?.image_url) {
            imageUrls.push(launch.rocket.configuration.image.image_url);
        }

        // Fourth priority: Agency logo
        if (launch.launch_service_provider?.logo?.image_url) {
            imageUrls.push(launch.launch_service_provider.logo.image_url);
        }

        // Fifth priority: Agency image
        if (launch.launch_service_provider?.image?.image_url) {
            imageUrls.push(launch.launch_service_provider.image.image_url);
        }

        // Look in program links for images
        if (launch.program && Array.isArray(launch.program)) {
            launch.program.forEach(program => {
                if (program.image?.image_url) {
                    imageUrls.push(program.image.image_url);
                }
            });
        }

        // Look for pad images
        if (launch.pad?.image?.image_url) {
            imageUrls.push(launch.pad.image.image_url);
        }

        // Fallback to map image if available
        if (launch.pad?.map_image) {
            imageUrls.push(launch.pad.map_image);
        }

        // For backward compatibility, check old format too
        // First priority: Mission patch (old format)
        if (launch.mission?.mission_patch) {
            imageUrls.push(launch.mission.mission_patch);
        }

        // Second priority: Image field (old format)
        if (typeof launch.image === 'string') {
            imageUrls.push(launch.image);
        }

        // Third priority: Rocket image (old format)
        if (launch.rocket?.configuration?.image_url) {
            imageUrls.push(launch.rocket.configuration.image_url);
        }

        // Fourth priority: Agency logo (old format)
        if (launch.launch_service_provider?.logo_url) {
            imageUrls.push(launch.launch_service_provider.logo_url);
        }

        // Fifth priority: Agency image (old format)
        if (launch.launch_service_provider?.image_url) {
            imageUrls.push(launch.launch_service_provider.image_url);
        }

        return imageUrls;
    }

    /**
     * Get image URL for a specific launch
     * @param {Object} launch - Launch object from API
     * @returns {Promise<String|null>} URL of the best image for this launch or null if no valid images
     */
    async function getLaunchImageUrl(launch) {
        if (!config.images.enabled) {
            return null;
        }

        // Check cache first if caching is enabled
        if (config.images.cacheImages) {
            const cacheKey = `launch_${launch.id}`;
            if (state.imageCache.has(cacheKey)) {
                return state.imageCache.get(cacheKey);
            }
        }

        console.log(`Looking for images for launch: ${launch.name} (ID: ${launch.id})`);

        // Extract all potential image URLs from the launch object
        const imageUrls = extractLaunchImages(launch);

        console.log(`Found ${imageUrls.length} potential images in launch data`);

        // Validate each URL until we find one that works
        for (const url of imageUrls) {
            console.log(`Checking image URL: ${url}`);
            if (await validateImageUrl(url)) {
                console.log(`Found valid image URL: ${url}`);

                // Cache the result if caching is enabled
                if (config.images.cacheImages) {
                    const cacheKey = `launch_${launch.id}`;
                    state.imageCache.set(cacheKey, url);
                }

                return url;
            }
        }

        // If no valid images found in launch data, try the default image
        if (config.images.fallbackToGeneric && await validateImageUrl(DEFAULT_LAUNCH_IMAGE)) {
            console.log(`No valid images found in launch data, using default: ${DEFAULT_LAUNCH_IMAGE}`);
            return DEFAULT_LAUNCH_IMAGE;
        }

        console.warn(`No valid images found for launch: ${launch.name}`);
        return null;
    }

    /**
     * Diagnostic function to log available images in a launch object
     * @param {Object} launch - Launch object from API
     */
    function logLaunchImageFields(launch) {
        console.log("\n==== AVAILABLE IMAGE FIELDS ====");
        console.log(`Launch: ${launch.name} (ID: ${launch.id})`);

        // Check various potential image locations (v2.3.0 format)
        console.log("Mission image:", launch.mission?.image?.image_url || "N/A");
        console.log("Launch image:", launch.image?.image_url || "N/A");
        console.log("Rocket image:", launch.rocket?.configuration?.image?.image_url || "N/A");
        console.log("Provider logo:", launch.launch_service_provider?.logo?.image_url || "N/A");
        console.log("Provider image:", launch.launch_service_provider?.image?.image_url || "N/A");
        console.log("Pad image:", launch.pad?.image?.image_url || "N/A");
        console.log("Pad map:", launch.pad?.map_image || "N/A");

        // Check for program images
        if (launch.program && Array.isArray(launch.program)) {
            launch.program.forEach((program, index) => {
                console.log(`Program ${index} image:`, program.image?.image_url || "N/A");
            });
        }

        // Check old format (v2.2.0) for backward compatibility
        console.log("\n-- Legacy Format (v2.2.0) --");
        console.log("Mission patch (old):", launch.mission?.mission_patch || "N/A");
        console.log("Launch image (old):", typeof launch.image === 'string' ? launch.image : "N/A");
        console.log("Rocket image (old):", launch.rocket?.configuration?.image_url || "N/A");
        console.log("Provider logo (old):", launch.launch_service_provider?.logo_url || "N/A");
        console.log("Provider image (old):", launch.launch_service_provider?.image_url || "N/A");

        console.log("================================\n");
    }

    /**
     * Clear the image cache
     * @returns {Number} Number of items cleared from cache
     */
    function clearImageCache() {
        const size = state.imageCache.size;
        state.imageCache.clear();
        return size;
    }

    return {
        getLaunchImageUrl,
        validateImageUrl,
        clearImageCache,
        logLaunchImageFields,
        DEFAULT_LAUNCH_IMAGE
    };
};