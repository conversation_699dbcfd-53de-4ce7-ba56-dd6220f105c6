// utils/api.js - API interaction functions with centralized rate limiting

const axios = require('axios');
const moment = require('moment');
const dns = require('dns');
module.exports = (config, state) => {

// Import logger with fallback
let logger;
let log;
try {
    logger = require('./logger');
    log = logger.getLogger('api');
} catch (e) {
    console.log('Logger not available, using console fallback');
    log = {
        error: (message, meta = {}) => console.error(message, meta),
        warn: (message, meta = {}) => console.warn(message, meta),
        info: (message, meta = {}) => console.log(message, meta),
        debug: (message, meta = {}) => (console.debug || console.log)(message, meta)
    };
}

// Central API call tracking - improved rate limiting system
const API_RATE_LIMIT = {
    MAX_CALLS: 15,       // Maximum calls per hour
    CALL_WINDOW: 60,     // minutes
    calls: [],
    lastWarningTime: null
};

/**
 * Check if an API call can be made based on rate limits
 * @returns {Boolean} Whether an API call is permitted
 */
function canMakeApiCall() {
    const now = moment();
    
    // Remove calls older than the rate limit window
    API_RATE_LIMIT.calls = API_RATE_LIMIT.calls.filter(
        callTime => now.diff(callTime, 'minutes') < API_RATE_LIMIT.CALL_WINDOW
    );
    
    // Check if we've exceeded max calls
    const canCall = API_RATE_LIMIT.calls.length < API_RATE_LIMIT.MAX_CALLS;
    
    // Log a warning periodically if rate limit is close
    if (!canCall && (!API_RATE_LIMIT.lastWarningTime || now.diff(API_RATE_LIMIT.lastWarningTime, 'minutes') >= 10)) {
        log.warn(`API RATE LIMIT APPROACHING: ${API_RATE_LIMIT.calls.length}/${API_RATE_LIMIT.MAX_CALLS} calls in the last hour`);
        API_RATE_LIMIT.lastWarningTime = now;
    }
    
    return canCall;
}

/**
 * Safely execute an API call with rate limiting and error handling
 * @param {Function} apiCall - Function that makes the actual API call
 * @param {Object} [options] - Additional options for the call
 * @returns {Promise} API call result or cached data
 */
async function safeApiCall(apiCall, options = {}) {
    const {
        fallbackToCached = true,
        logError = true,
        forceCall = false
    } = options;

    // Check if API call is allowed
    if (!forceCall && !canMakeApiCall()) {
        if (fallbackToCached && state.launchesCache.data.length > 0) {
            log.info('Rate limit reached. Using cached data.');
            return state.launchesCache.data;
        }
        return [];
    }

    try {
        // Record the API call attempt
        API_RATE_LIMIT.calls.push(moment());
        
        // Execute the API call
        const result = await apiCall();
        return result;
    } catch (error) {
        if (logError) {
            log.error('API Call Error', { error: error.message, stack: error.stack });
        }
        
        // Handle rate limit (429) or server errors
        if (error.response) {
            switch (error.response.status) {
                case 429: // Too Many Requests
                    const retryAfter = error.response.headers['retry-after'] || 960;
                    log.warn(`Rate limit exceeded. Retry after ${retryAfter} seconds`);
                    break;
                case 500: // Internal Server Error
                case 502: // Bad Gateway
                case 503: // Service Unavailable
                case 504: // Gateway Timeout
                    log.warn('Server error occurred. Falling back to cached data.');
                    break;
            }
        }
        
        // Fallback to cached data if configured
        if (fallbackToCached && state.launchesCache.data.length > 0) {
            return state.launchesCache.data;
        }
        
        return [];
    }
}

/**
 * Make API request with retry logic (backwards compatibility)
 * @param {String} url - API endpoint URL
 * @param {Object} [params] - Request parameters
 * @param {Number} [retries] - Number of retry attempts
 * @returns {Promise} API response or null
 */
async function makeApiRequestWithRetry(url, params = {}, retries = 3) {
    return safeApiCall(async () => {
        log.info('Making API request with retry', { url, params, retries });
        const response = await axios.get(url, {
            params,
            timeout: config.api.requestTimeout || 10000
        });
        return response.data;
    }, {
        fallbackToCached: false,
        logError: true
    });
}

/**
 * Perform DNS lookup with promise interface
 * @param {String} hostname - Hostname to resolve
 * @returns {Promise<Array>} Array of IP addresses
 */
function dnsLookup(hostname) {
    return new Promise((resolve, reject) => {
        dns.resolve4(hostname, (err, addresses) => {
            if (err) {
                reject(err);
            } else {
                resolve(addresses);
            }
        });
    });
}

/**
 * Fetch upcoming launches from Space Devs API with caching and rate limiting
 * @returns {Promise<Array>} Array of launch objects
 */
async function fetchUpcomingLaunches() {
    const now = moment();
    
    // Check if cache is valid
    if (
        state.launchesCache.timestamp &&
        now.diff(state.launchesCache.timestamp, 'minutes') < config.api.cacheDuration
    ) {
        log.debug('Using cached launch data', {
            cacheAge: now.diff(state.launchesCache.timestamp, 'minutes'),
            cacheDuration: config.api.cacheDuration,
            cacheSize: state.launchesCache.data.length
        });
        return state.launchesCache.data;
    }

    return safeApiCall(async () => {
        log.info('Fetching fresh launch data from API', {
            url: config.api.launchApiUrl,
            timeWindow: '24 hours'
        });
        
        const response = await axios.get(config.api.launchApiUrl, {
            params: {
                limit: config.api.maxResults,
                ordering: 'net',
                net__gte: now.toISOString(),
                net__lte: moment(now).add(24, 'hours').toISOString()
            },
            timeout: config.api.requestTimeout
        });
        
        log.debug('API response received', {
            status: response.status,
            count: response.data.results.length
        });
        
        // Update cache
        state.launchesCache = {
            data: response.data.results,
            timestamp: moment()
        };
        
        return response.data.results;
    });
}

/**
 * Fetch launches for a specific period
 * @param {moment} startDate - Period start date
 * @param {moment} endDate - Period end date
 * @returns {Promise<Array>} Array of launch objects
 */
async function fetchLaunchesForPeriod(startDate, endDate) {
    // If we have recent cached data that covers this period, use it
    if (
        state.launchesCache.timestamp &&
        state.launchesCache.data.length > 0 &&
        moment().diff(state.launchesCache.timestamp, 'minutes') < config.api.cacheDuration
    ) {
        const cachedLaunches = state.launchesCache.data.filter(launch => {
            const launchDate = moment(launch.net);
            return launchDate.isBetween(startDate, endDate, null, '[]');
        });

        if (cachedLaunches.length > 0) {
            log.info('Using cached launches for the period', {
                startDate: startDate.format('YYYY-MM-DD'),
                endDate: endDate.format('YYYY-MM-DD'),
                count: cachedLaunches.length
            });
            return cachedLaunches;
        }
    }

    return safeApiCall(async () => {
        log.info('Fetching launches for period', {
            startDate: startDate.format('YYYY-MM-DD'),
            endDate: endDate.format('YYYY-MM-DD')
        });

        const response = await axios.get(config.api.launchApiUrl, {
            params: {
                limit: 20, // Increase limit to get more launches
                ordering: 'net',
                net__gte: startDate.toISOString(),
                net__lte: endDate.toISOString()
            },
            timeout: config.api.requestTimeout
        });

        log.debug('Period API response received', {
            status: response.status,
            count: response.data.results.length
        });

        // Update cache
        state.launchesCache = {
            data: response.data.results,
            timestamp: moment()
        };

        return response.data.results;
    });
}

/**
 * Get latest data for a specific launch by ID
 * @param {String} launchId - Launch ID
 * @returns {Promise<Object>} Launch object or null if error
 */
async function getLatestLaunchData(launchId) {
    return safeApiCall(async () => {
        log.info('Fetching latest launch data', { launchId });

        const response = await axios.get(`${config.api.launchByIdUrl}${launchId}/`, {
            timeout: config.api.requestTimeout
        });
        
        log.debug('Latest launch data received', {
            launchId,
            status: response.status,
            launchName: response.data.name
        });

        return response.data;
    }, {
        fallbackToCached: false,
        logError: true
    });
}

/**
 * Get API rate limit status for monitoring
 * @returns {Object} Rate limit status information
 */
function getApiRateLimitStatus() {
    const now = moment();
    
    // Clean up old calls for accurate count
    API_RATE_LIMIT.calls = API_RATE_LIMIT.calls.filter(
        callTime => now.diff(callTime, 'minutes') < API_RATE_LIMIT.CALL_WINDOW
    );
    
    return {
        callsInLastHour: API_RATE_LIMIT.calls.length,
        maxCalls: API_RATE_LIMIT.MAX_CALLS,
        remainingCalls: API_RATE_LIMIT.MAX_CALLS - API_RATE_LIMIT.calls.length,
        windowMinutes: API_RATE_LIMIT.CALL_WINDOW,
        lastCallTime: API_RATE_LIMIT.calls.length > 0 ? 
            API_RATE_LIMIT.calls[API_RATE_LIMIT.calls.length - 1].format('YYYY-MM-DD HH:mm:ss') : 
            'No calls made',
        rateLimitReached: API_RATE_LIMIT.calls.length >= API_RATE_LIMIT.MAX_CALLS
    };
}

return {
    fetchUpcomingLaunches,
    fetchLaunchesForPeriod,
    getLatestLaunchData,
    getApiRateLimitStatus,
    makeApiRequestWithRetry,
    dnsLookup
  };
};