#!/bin/bash

set -e # Exit immediately if a command exits with a non-zero status.

# Determine environment from argument
ENV=$1
if [ -z "$ENV" ]; then
    echo "Usage: $0 <dev|prod>"
    exit 1
fi

# --- Run Tests ---
echo "🧪 Running tests to ensure code quality..."
TEST_RESULTS=$(npm test 2>&1)
TEST_SUMMARY=$(echo "$TEST_RESULTS" | grep -E "(passing|failing|pending)" | tail -n 1)

if [ -z "$TEST_SUMMARY" ]; then
    TEST_SUMMARY="Tests completed, but summary not found."
fi

echo "✅ All tests passed. Summary: ${TEST_SUMMARY}"


# --- Run Tests ---
echo "🧪 Running tests to ensure code quality..."
npm test
echo "✅ All tests passed."

TARGET_BRANCH=""
if [ "$ENV" == "dev" ]; then
    TARGET_BRANCH="DEV"
elif [ "$ENV" == "prod" ]; then
    TARGET_BRANCH="main"
else
    echo "Invalid environment: $ENV. Must be 'dev' or 'prod'."
    exit 1
fi

echo "🚀 Starting 'Ship It' process for $ENV environment..."

# --- 1. Get and Increment Version ---

# Get current date in YYYY.MM.DD format
CURRENT_DATE=$(date +"%Y.%m.%d")

NEW_VERSION=""

if [ "$ENV" == "dev" ]; then
    # For dev, use YYYY.MM.DD.githash
    GIT_COMMIT_ID=$(git rev-parse --short HEAD)
    NEW_VERSION="${CURRENT_DATE}.${GIT_COMMIT_ID}"
elif [ "$ENV" == "prod" ]; then
    # For prod, use YYYY.MM.DD.BUILD_NUMBER
    # Always start with build 1 for a new day, or increment if already built today
    CURRENT_PROD_VERSION=$(git log origin/main --grep="^chore(release): ship it" --pretty=format:"%s" -n 1 | grep -oE "[0-9]{4}\.[0-9]{2}\.[0-9]{2}\.[0-9]+" | head -1)
    
    NEW_BUILD_NUMBER=1
    if [ -n "$CURRENT_PROD_VERSION" ]; then
        IFS='.' read -r -a VERSION_PARTS <<< "$CURRENT_PROD_VERSION"
        VERSION_DATE="${VERSION_PARTS[0]}.${VERSION_PARTS[1]}.${VERSION_PARTS[2]}"
        CURRENT_BUILD_NUMBER=${VERSION_PARTS[3]}

        if [ "$VERSION_DATE" == "$CURRENT_DATE" ]; then
            NEW_BUILD_NUMBER=$((CURRENT_BUILD_NUMBER + 1))
        fi
    fi
    NEW_VERSION="${CURRENT_DATE}.${NEW_BUILD_NUMBER}"
fi

echo "New Version: ${NEW_VERSION}"

# --- 2. Update Version Numbers in Files ---

echo "Updating version numbers in package.json, config.yaml, README.md and Dockerfile..."

# Update root package.json
sed -i '' "s/\"version\": \".*\"/\"version\": \"${NEW_VERSION}\"/g" package.json

# Update config.yaml
sed -i '' "s/version: \".*\"/version: \"${NEW_VERSION}\"/g" config.yaml

# Update config.dev.yaml
if [ -f config.dev.yaml ]; then
    sed -i '' "s/version: \".*\"/version: \"${NEW_VERSION}\"/g" config.dev.yaml
fi

# Update README.md
sed -i '' "s/\*\*Current Version:\*\*:.*/\*\*Current Version:\*\* ${NEW_VERSION}/g" README.md

# Update Dockerfile
sed -i '' "s/ARG BUILD_VERSION=.*/ARG BUILD_VERSION=${NEW_VERSION}/g" Dockerfile

# Update config.yaml - remove (DEV) from name for prod environment
if [ "$ENV" == "dev" ]; then
    sed -i '' "s#^name: .*#name: \"Space Launch Notifier (DEV)\"#g" config.yaml
elif [ "$ENV" == "prod" ]; then
    sed -i '' "s#^name: .*#name: \"Space Launch Notifier\"#g" config.yaml
fi

# Update Dashboard header
if [ "$ENV" == "dev" ]; then
    sed -i '' "s#<h1 id=\"environment\">.*</h1>#<h1 id=\"environment\">Space Launch Notifier (DEV)</h1>#g" dashboard/index.html
elif [ "$ENV" == "prod" ]; then
    sed -i '' "s#<h1 id=\"environment\">.*</h1>#<h1 id=\"environment\">Space Launch Notifier</h1>#g" dashboard/index.html
fi



# --- Handle SLN_ENV in config.yaml ---
echo "Updating SLN_ENV in config.yaml based on environment (${ENV})..."
if [ "$ENV" == "dev" ]; then
    # Ensure SLN_ENV: DEV is present
    # If 'environment:' block is not found after 'map:', add it.
    # This assumes 'map:' is a reliable anchor.
    if ! grep -q "environment:" config.yaml; then
        sed -i '' '/^map:/a\
  environment:\
    SLN_ENV: DEV' config.yaml
    else
        # If 'environment:' block exists, ensure SLN_ENV is DEV
        sed -i '' 's/SLN_ENV: .*/SLN_ENV: DEV/g' config.yaml
    fi
    echo "SLN_ENV set to DEV in config.yaml."
elif [ "$ENV" == "prod" ]; then
    # Remove the 'environment:' block and its immediate child (SLN_ENV: DEV)
    # This assumes 'environment:' has only one child line.
    if grep -q "^environment:" config.yaml; then
        sed -i '' '/^environment:/ { N; d; }' config.yaml
        echo "Removed environment block from config.yaml."
    else
        echo "Environment block not found in config.yaml. No changes needed."
    fi
fi

echo "Version numbers updated."






# --- 3. Update Changelog ---

CHANGELOG_FILE="CHANGELOG.md"
TEMP_CHANGELOG_FILE="${CHANGELOG_FILE}.tmp"

# Fetch the latest from the remote to ensure we have the correct reference
echo "Fetching latest changes from remote..."
git fetch origin

# --- 4. Git Commit and Push ---

echo "Committing changes..."
git add .
git commit -m "chore(release): ship it ${NEW_VERSION}"

# Generate changelog after the commit
echo "Generating changelog for ${ENV} environment..."

CHANGELOG_MESSAGES=""
if [ "$ENV" == "dev" ]; then
    # For dev, get all commits since the last "ship it" commit on the local DEV branch
    LAST_SHIP_IT_COMMIT_HASH=$(git log --grep="^chore(release): ship it" --skip=1 -n 1 --pretty=format:%H)

    if [ -z "$LAST_SHIP_IT_COMMIT_HASH" ]; then
        echo "No previous 'ship it' commit found. Using last 10 commits for changelog."
        CHANGELOG_MESSAGES=$(git log --pretty=format:"- %s" -n 10 --no-merges | grep -v "^- chore(release): ship it" || true)
    else
        echo "Finding changes since last ship it commit: ${LAST_SHIP_IT_COMMIT_HASH}"
        # The HEAD~1 is important to exclude the current "ship it" commit itself from the log
        CHANGELOG_MESSAGES=$(git log --pretty=format:"- %s" "${LAST_SHIP_IT_COMMIT_HASH}"..HEAD~1 --no-merges | grep -v "^- chore(release): ship it" || true)
    fi

    # Fallback if no specific changes are found
    if [ -z "$CHANGELOG_MESSAGES" ]; then
        CHANGELOG_MESSAGES="- No new changes to report."
    fi
elif [ "$ENV" == "prod" ]; then
    # For prod, get feature commits since the last merge with DEV
    MERGE_BASE=$(git merge-base HEAD origin/DEV)
    CHANGELOG_MESSAGES=$(git log --pretty=format:"- %s" "${MERGE_BASE}"..HEAD --grep="^feat:" --no-merges)
fi

# Fallback if no specific changes are found
if [ -z "$CHANGELOG_MESSAGES" ]; then
    CHANGELOG_MESSAGES="- No new changes to report."
fi

# Extract a one-line summary from the changelog messages
CHANGELOG_SUMMARY=$(echo "${CHANGELOG_MESSAGES}" | head -n 1 | sed 's/^- //')
if [ -z "$CHANGELOG_SUMMARY" ] || [ "$CHANGELOG_SUMMARY" == "No new changes to report." ]; then
    CHANGELOG_SUMMARY="No significant changes."
fi

# Format the changelog entry
CHANGELOG_ENTRY="## [${NEW_VERSION}] - $(date +"%Y-%m-%d") ${CHANGELOG_SUMMARY}\n\n### Changed\n\n${CHANGELOG_MESSAGES}\n\n"

echo "Generated Changelog Entry:"
echo "${CHANGELOG_ENTRY}"
echo "${CHANGELOG_ENTRY}" > changelog_entry.txt

# Prepend new entry to changelog
(echo "${CHANGELOG_ENTRY}"; cat "${CHANGELOG_FILE}") > "${TEMP_CHANGELOG_FILE}"
mv "${TEMP_CHANGELOG_FILE}" "${CHANGELOG_FILE}"

# Amend the commit with the updated changelog
git add CHANGELOG.md
git commit --amend --no-edit

echo "Pushing to GitHub branch: $TARGET_BRANCH..."
git push origin $TARGET_BRANCH

echo "✅ 'Ship It' process completed successfully for version ${NEW_VERSION}!"
