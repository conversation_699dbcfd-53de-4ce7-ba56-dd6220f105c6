#!/bin/bash

# --- Configuration (Set these as environment variables or fill in directly) ---
# export HA_IP="your_home_assistant_ip"
# export HA_SSH_USER="your_ssh_username"
# export HA_SSH_KEY_PATH="/path/to/your/ssh/private/key" # Optional, if using key-based auth
# export HA_SSH_PASSWORD="your_ssh_password" # Optional, if using password-based auth

ADDON_SLUG="space-launch-notifier"

# --- Check for required environment variables ---
if [ -z "$HA_IP" ]; then
  echo "Error: HA_IP environment variable not set."
  exit 1
fi

if [ -z "$HA_SSH_USER" ]; then
  echo "Error: HA_SSH_USER environment variable not set."
  exit 1
fi

# --- Construct SSH command ---
SSH_COMMAND="ssh"

if [ -n "$HA_SSH_KEY_PATH" ]; then
  SSH_COMMAND+=" -i \"$HA_SSH_KEY_PATH\""
elif [ -n "$HA_SSH_PASSWORD" ]; then
  # Using sshpass for password-based authentication (requires sshpass to be installed)
  # This is less secure than SSH keys and not recommended for automation.
  # You might need to install sshpass: `sudo apt-get install sshpass` or `brew install sshpass`
  SSH_COMMAND="sshpass -p \"$HA_SSH_PASSWORD\" $SSH_COMMAND"
else
  echo "Warning: No SSH key path or password provided. SSH might prompt for password."
fi

SSH_COMMAND+=" ${HA_SSH_USER}@${HA_IP}"

# --- Execute commands on Home Assistant ---
echo "Attempting to update and restart Home Assistant add-on: ${ADDON_SLUG}"

# Update the add-on
echo "Running: ${SSH_COMMAND} \"ha addons update ${ADDON_SLUG}\""
${SSH_COMMAND} "ha addons update ${ADDON_SLUG}"

if [ $? -eq 0 ]; then
  echo "Add-on update command sent successfully."
  # Restart the add-on
  echo "Running: ${SSH_COMMAND} \"ha addons restart ${ADDON_SLUG}\""
  ${SSH_COMMAND} "ha addons restart ${ADDON_SLUG}"
  if [ $? -eq 0 ]; then
    echo "Add-on restart command sent successfully."
    echo "Update and restart process completed."
  else
    echo "Error: Failed to send add-on restart command."
  fi
else
  echo "Error: Failed to send add-on update command."
fi
