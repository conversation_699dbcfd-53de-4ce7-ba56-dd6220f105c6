#!/bin/bash

# Script to set up the repository structure for a Home Assistant add-on

echo "Setting up Home Assistant add-on repository structure..."

# Create the add-on directory if it doesn't exist
mkdir -p space-launch-notifier

# Copy necessary files to the add-on directory
echo "Copying add-on files to space-launch-notifier directory..."
cp config.yaml Dockerfile run.sh build.yaml space-launch-notifier/

# Create a symbolic link for the main application files
echo "Creating symbolic links for application files..."
cd space-launch-notifier
ln -sf ../index.js .
ln -sf ../package.json .
ln -sf ../package-lock.json .
ln -sf ../utils .
ln -sf ../dashboard .
ln -sf ../public .
ln -sf ../views .
cd ..

# Create repository.json if it doesn't exist
if [ ! -f "repository.json" ]; then
  echo "Creating repository.json..."
  cat > repository.json << 'EOF'
{
  "name": "Space Launch Notifier Add-on Repository",
  "url": "https://github.com/kenibarwick/space-launch-notifier",
  "maintainer": "<PERSON><PERSON> <<EMAIL>>"
}
EOF
fi

# Update README.md in the root directory
echo "Updating root README.md..."
cat > README.md << 'EOF'
# Space Launch Notifier - Home Assistant Add-on Repository

This repository contains the Space Launch Notifier add-on for Home Assistant, which provides WhatsApp notifications for upcoming rocket launches with livestreams.

**Current Version:** 0.0.42 ([Changelog](./CHANGELOG.md))

## Installation

### For Personal Use (Private Repository)

Since this is a private repository, you'll need to use a Personal Access Token (PAT) to access it:

1. Create a Personal Access Token in GitHub:

   - Go to GitHub Settings > Developer settings > Personal access tokens
   - Click "Generate new token (classic)"
   - Give it a name like "Home Assistant Add-on"
   - Select the "repo" scope (to access private repositories)
   - Click "Generate token" and copy the token
   - For detailed instructions, see [GitHub Token Setup Guide](./docs/github-token-setup.md)

2. Add the repository to Home Assistant:

   - Navigate to the Home Assistant Add-on Store
   - Click the menu in the top right and select "Repositories"
   - Add the repository URL with your token using this EXACT format:
     ```
     https://kenibarwick:<EMAIL>/kenibarwick/space-launch-notifier.git
     ```
   - Replace `YOUR_TOKEN` with the token you generated (no other changes to the URL)
   - Make sure to include the `.git` at the end of the URL
   - Click "Add"

3. Install the add-on:
   - The "Space Launch Notifier" add-on should now appear in the add-on store
   - Click on it and then click "Install"
   - Configure the required settings (especially WhatsApp Group ID)
   - Start the add-on

## Available Add-ons

### [Space Launch Notifier](./space-launch-notifier)

Get WhatsApp notifications for upcoming rocket launches with livestreams.

**Features:**

- 🚀 Monitors upcoming rocket launches
- 📱 Sends WhatsApp notifications when a launch with a livestream is about to happen
- 📊 Provides a dashboard for monitoring the application status
- 📅 Sends daily digests of upcoming launches
- 🌐 Supports multiple languages
EOF

echo "Home Assistant add-on repository structure setup complete!"
echo ""
echo "Next steps:"
echo "1. Review the changes: git status"
echo "2. Commit the changes: git add -A && git commit -m \"Set up Home Assistant add-on repository structure\""
echo "3. Push the changes: git push"
echo "4. Add the repository to Home Assistant using the URL format:"
echo "   https://kenibarwick:<EMAIL>/kenibarwick/space-launch-notifier.git"
