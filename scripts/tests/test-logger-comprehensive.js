// test-logger-comprehensive.js - Comprehensive test script for the logger module
// This script tests all aspects of the logging system

const fs = require('fs');
const path = require('path');
const os = require('os');

// Import the logger
const logger = require('./utils/logger');

// Create a logger instance for this module
const log = logger.getLogger('test-logger-comprehensive');

// Helper function to create a separator line
function separator(title) {
    const line = '='.repeat(50);
    console.log(`\n${line}`);
    console.log(` ${title} `.padStart((line.length + title.length) / 2, '=').padEnd(line.length, '='));
    console.log(`${line}\n`);
}

// Helper function to simulate Home Assistant environment
function simulateHomeAssistant() {
    // Create mock directories that would exist in Home Assistant
    const tempDir = fs.mkdtempSync(path.join(os.tmpdir(), 'ha-test-'));
    fs.mkdirSync(path.join(tempDir, 'config'), { recursive: true });
    fs.mkdirSync(path.join(tempDir, 'share'), { recursive: true });
    fs.mkdirSync(path.join(tempDir, 'data'), { recursive: true });
    fs.mkdirSync(path.join(tempDir, 'data', 'logs'), { recursive: true });
    
    return {
        tempDir,
        cleanup: () => {
            try {
                fs.rmSync(tempDir, { recursive: true, force: true });
                console.log(`Cleaned up temporary directory: ${tempDir}`);
            } catch (error) {
                console.error(`Failed to clean up temporary directory: ${error.message}`);
            }
        }
    };
}

// Main test function
async function runTests() {
    separator('LOGGER COMPREHENSIVE TEST');
    
    // 1. Test basic logging functionality
    separator('1. BASIC LOGGING FUNCTIONALITY');
    
    console.log('Testing all log levels:');
    log.error('This is an ERROR message');
    log.warn('This is a WARNING message');
    log.info('This is an INFO message');
    log.http('This is an HTTP message');
    log.verbose('This is a VERBOSE message');
    log.debug('This is a DEBUG message');
    log.silly('This is a SILLY message');
    
    // 2. Test logging with metadata
    separator('2. LOGGING WITH METADATA');
    
    console.log('Testing logging with metadata:');
    log.info('Message with simple metadata', { user: 'test-user' });
    log.info('Message with nested metadata', { 
        user: { 
            id: 123, 
            name: 'Test User', 
            roles: ['admin', 'user'] 
        },
        timestamp: new Date().toISOString()
    });
    
    // 3. Test error object logging
    separator('3. ERROR OBJECT LOGGING');
    
    console.log('Testing error object logging:');
    try {
        throw new Error('Test error with stack trace');
    } catch (error) {
        log.error('Caught an error', {
            error: error.message,
            stack: error.stack
        });
    }
    
    // 4. Test different module contexts
    separator('4. DIFFERENT MODULE CONTEXTS');
    
    console.log('Testing different module contexts:');
    const logModuleA = logger.getLogger('module-a');
    const logModuleB = logger.getLogger('module-b');
    const logModuleC = logger.getLogger('module-c');
    
    logModuleA.info('This is a log from Module A');
    logModuleB.warn('This is a warning from Module B');
    logModuleC.error('This is an error from Module C');
    
    // 5. Test environment detection
    separator('5. ENVIRONMENT DETECTION');
    
    console.log(`Current environment: ${logger.isHomeAssistant() ? 'Home Assistant' : 'Standalone'}`);
    console.log(`Log directory: ${logger.getLogDir()}`);
    
    // 6. Test log file operations
    separator('6. LOG FILE OPERATIONS');
    
    console.log('Listing log files:');
    const logFiles = await logger.getLogFiles();
    logFiles.forEach((file, index) => {
        console.log(`${index + 1}. ${file.name}`);
    });
    
    if (logFiles.length > 0) {
        console.log(`\nReading first log file (${logFiles[0].name}):`);
        const content = await logger.readLogFile(logFiles[0].name);
        console.log(`File size: ${content.length} bytes`);
        console.log('First 200 characters:');
        console.log(content.substring(0, 200) + '...');
    }
    
    // 7. Test log rotation (simulated)
    separator('7. LOG ROTATION (SIMULATED)');
    
    console.log('Generating many log entries to test rotation:');
    for (let i = 0; i < 100; i++) {
        log.info(`Log entry #${i} for rotation testing`, { index: i });
    }
    console.log('Generated 100 log entries');
    
    // 8. Test Home Assistant environment (simulated)
    separator('8. HOME ASSISTANT ENVIRONMENT (SIMULATED)');
    
    console.log('Simulating Home Assistant environment:');
    const { tempDir, cleanup } = simulateHomeAssistant();
    
    console.log(`Created temporary Home Assistant directories in: ${tempDir}`);
    console.log('Note: This is just a simulation. To fully test Home Assistant integration,');
    console.log('the application needs to be run in an actual Home Assistant environment.');
    
    // Clean up temporary directories
    cleanup();
    
    // 9. Test system information logging
    separator('9. SYSTEM INFORMATION LOGGING');
    
    console.log('Logging system information:');
    const systemInfo = {
        platform: os.platform(),
        arch: os.arch(),
        nodeVersion: process.version,
        hostname: os.hostname(),
        uptime: os.uptime(),
        memory: {
            total: os.totalmem(),
            free: os.freemem()
        },
        cpus: os.cpus().length
    };
    
    log.info('System information', { systemInfo });
    
    // Test complete
    separator('TEST COMPLETE');
    
    console.log('Logger comprehensive test completed successfully!');
    console.log('Check the log files in the data/logs directory for the full output.');
}

// Run the tests
runTests().catch(error => {
    console.error('Test failed:', error);
});
