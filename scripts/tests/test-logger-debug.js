// test-logger-debug.js - Test script for the logger module in debug mode
// This script tests the logger with debug mode enabled

// Set debug mode environment variable
process.env.DEBUG = 'true';
process.env.LOG_LEVEL = 'debug';
process.env.LOG_CONSOLE_LEVEL = 'debug';
process.env.LOG_FILE_LEVEL = 'debug';

// Import the logger
const logger = require('./utils/logger');

// Create a logger instance for this module
const log = logger.getLogger('test-logger-debug');

// Helper function to create a separator line
function separator(title) {
    const line = '='.repeat(50);
    console.log(`\n${line}`);
    console.log(` ${title} `.padStart((line.length + title.length) / 2, '=').padEnd(line.length, '='));
    console.log(`${line}\n`);
}

// Main test function
async function runTest() {
    separator('LOGGER DEBUG MODE TEST');
    
    // Test all log levels
    console.log('Testing all log levels in debug mode:');
    log.error('This is an ERROR message in debug mode');
    log.warn('This is a WARNING message in debug mode');
    log.info('This is an INFO message in debug mode');
    log.http('This is an HTTP message in debug mode');
    log.verbose('This is a VERBOSE message in debug mode');
    log.debug('This is a DEBUG message in debug mode');
    log.silly('This is a SILLY message in debug mode');
    
    // Test debug-specific logging
    separator('DEBUG-SPECIFIC LOGGING');
    
    console.log('Testing debug-specific logging:');
    log.debug('This debug message should only appear in debug mode');
    log.debug('Debug message with object', { 
        debugInfo: true,
        timestamp: new Date().toISOString(),
        details: {
            level: 'debug',
            visible: 'only in debug mode'
        }
    });
    
    // Test debug log file
    separator('DEBUG LOG FILE');
    
    console.log('Checking for debug log file:');
    const logFiles = await logger.getLogFiles();
    const debugLogFile = logFiles.find(file => file.name.startsWith('debug-'));
    
    if (debugLogFile) {
        console.log(`Found debug log file: ${debugLogFile.name}`);
        
        // Read the debug log file
        const content = await logger.readLogFile(debugLogFile.name);
        console.log(`Debug log file size: ${content.length} bytes`);
        console.log('First 200 characters:');
        console.log(content.substring(0, 200) + '...');
    } else {
        console.log('No debug log file found. This is unexpected in debug mode.');
    }
    
    // Test complete
    separator('TEST COMPLETE');
    
    console.log('Logger debug mode test completed!');
    console.log('Check the debug log file in the data/logs directory.');
}

// Run the test
runTest().catch(error => {
    console.error('Test failed:', error);
});
