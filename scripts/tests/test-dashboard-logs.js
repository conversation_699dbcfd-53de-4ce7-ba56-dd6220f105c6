// test-dashboard-logs.js - Test script for the dashboard log viewer
// This script tests the dashboard's log viewing functionality

const dashboard = require('./dashboard');
const logger = require('./utils/logger');
const fs = require('fs');
const path = require('path');
const axios = require('axios');
const io = require('socket.io-client');

// Create a logger instance for this test
const log = logger.getLogger('test-dashboard-logs');

// Helper function to create a separator line
function separator(title) {
    const line = '='.repeat(50);
    console.log(`\n${line}`);
    console.log(` ${title} `.padStart((line.length + title.length) / 2, '=').padEnd(line.length, '='));
    console.log(`${line}\n`);
}

// Generate test logs
async function generateTestLogs() {
    log.info('Generating test logs for dashboard testing');

    // Generate logs at different levels
    log.error('Test ERROR message for dashboard testing');
    log.warn('Test WARNING message for dashboard testing');
    log.info('Test INFO message for dashboard testing');
    log.debug('Test DEBUG message for dashboard testing');

    // Generate logs with metadata
    log.info('Test message with metadata', {
        test: true,
        timestamp: new Date().toISOString(),
        user: 'dashboard-test'
    });

    // Generate an error log with stack trace
    try {
        throw new Error('Test error for dashboard');
    } catch (error) {
        log.error('Test error with stack trace', {
            error: error.message,
            stack: error.stack
        });
    }

    // Generate logs from different modules
    const logModuleA = logger.getLogger('module-a');
    const logModuleB = logger.getLogger('module-b');

    logModuleA.info('Test log from Module A');
    logModuleB.warn('Test warning from Module B');

    log.info('Finished generating test logs');
}

// Test dashboard API endpoints for logs
async function testDashboardAPI(port) {
    try {
        separator('TESTING DASHBOARD API ENDPOINTS');

        // Test /api/logs endpoint
        console.log('Testing /api/logs endpoint...');
        const logsResponse = await axios.get(`http://localhost:${port}/api/logs`);
        console.log(`Found ${logsResponse.data.length} log files:`);
        logsResponse.data.forEach((file, index) => {
            console.log(`${index + 1}. ${file.name}`);
        });

        if (logsResponse.data.length > 0) {
            // Test /api/logs/:filename endpoint
            const firstLogFile = logsResponse.data[0].name;
            console.log(`\nTesting /api/logs/${firstLogFile} endpoint...`);
            const logContentResponse = await axios.get(`http://localhost:${port}/api/logs/${firstLogFile}`);
            console.log(`Retrieved log file content (${logContentResponse.data.length} characters)`);

            // Test /api/logs/:filename with JSON format and filtering
            console.log(`\nTesting /api/logs/${firstLogFile}?format=json endpoint...`);
            const jsonLogResponse = await axios.get(`http://localhost:${port}/api/logs/${firstLogFile}?format=json`);
            console.log(`Retrieved ${jsonLogResponse.data.length} log entries in JSON format`);

            // Test level filtering
            console.log(`\nTesting level filtering with /api/logs/${firstLogFile}?format=json&level=error endpoint...`);
            const errorLogsResponse = await axios.get(`http://localhost:${port}/api/logs/${firstLogFile}?format=json&level=error`);
            console.log(`Retrieved ${errorLogsResponse.data.length} ERROR level log entries`);

            // Test search filtering
            console.log(`\nTesting search filtering with /api/logs/${firstLogFile}?format=json&search=test endpoint...`);
            const searchLogsResponse = await axios.get(`http://localhost:${port}/api/logs/${firstLogFile}?format=json&search=test`);
            console.log(`Retrieved ${searchLogsResponse.data.length} log entries containing "test"`);
        }

        return true;
    } catch (error) {
        console.error('Error testing dashboard API:', error.message);
        return false;
    }
}

// Test Socket.IO log streaming
async function testSocketIO(port) {
    return new Promise((resolve, reject) => {
        try {
            separator('TESTING SOCKET.IO LOG STREAMING');

            console.log('Connecting to Socket.IO...');
            const socket = io(`http://localhost:${port}`);

            socket.on('connect', () => {
                console.log('Connected to Socket.IO');
                console.log('Socket.IO connection test successful');

                // Disconnect after a short delay
                setTimeout(() => {
                    socket.disconnect();
                    resolve(true);
                }, 1000);
            });

            socket.on('connect_error', (error) => {
                console.error('Socket.IO connection error:', error.message);
                reject(error);
            });

            // Set a timeout in case the test hangs
            setTimeout(() => {
                socket.disconnect();
                reject(new Error('Socket.IO test timed out'));
            }, 5000);
        } catch (error) {
            reject(error);
        }
    });
}

// Main test function
async function runTest() {
    separator('DASHBOARD LOG VIEWER TEST');

    // Generate test logs
    await generateTestLogs();

    // Start the dashboard
    console.log('Starting dashboard server...');
    const port = process.env.DASHBOARD_PORT || 3030;
    dashboard.startDashboard();

    console.log(`Dashboard running at http://localhost:${port}`);
    console.log('Waiting for dashboard to initialize...');

    // Wait for the dashboard to start
    await new Promise(resolve => setTimeout(resolve, 3000));

    // Test dashboard API endpoints
    const apiTestResult = await testDashboardAPI(port);

    // Test Socket.IO log streaming if API test was successful
    if (apiTestResult) {
        await testSocketIO(port);
    }

    separator('TEST COMPLETE');
    console.log('Dashboard log viewer test completed.');
    console.log(`You can manually check the dashboard at http://localhost:${port}`);
    console.log('Press Ctrl+C to stop the dashboard server.');
}

// Run the test
runTest().catch(error => {
    console.error('Test failed:', error);
});
