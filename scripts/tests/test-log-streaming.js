// test-log-streaming.js - Comprehensive test for dashboard log streaming

const { spawn } = require('child_process');
const path = require('path');
const fs = require('fs').promises;
const axios = require('axios');
const io = require('socket.io-client');

const LOG_DIR = path.join(__dirname, 'data', 'logs');
const TEST_LOG_FILE = path.join(LOG_DIR, 'test-log-streaming.log');
const DASHBOARD_PORT = 3030;

// Helper to clear and write to a log file
async function writeLog(message) {
    await fs.appendFile(TEST_LOG_FILE, `${new Date().toISOString()} INFO: ${message}\n`);
}

// Helper to clear log file
async function clearLogFile() {
    await fs.writeFile(TEST_LOG_FILE, '');
}

async function runTest() {
    console.log('\n--- Starting Log Streaming Test ---');

    // 1. Clean up previous test logs
    console.log('1. Cleaning up previous test logs...');
    await fs.mkdir(LOG_DIR, { recursive: true }).catch(() => {}); // Ensure dir exists
    await clearLogFile();
    console.log('   Done.');

    // 2. Start the dashboard in a child process
    console.log(`2. Starting dashboard on port ${DASHBOARD_PORT}...`);
    const dashboardProcess = spawn('node', ['dashboard.js'], {
        cwd: path.join(__dirname, ''), // Run from the root of the project
        env: { ...process.env, DASHBOARD_PORT: DASHBOARD_PORT },
        stdio: 'inherit' // Inherit stdio to see dashboard logs
    });

    // Wait for dashboard to start (give it some time)
    await new Promise(resolve => setTimeout(resolve, 5000));
    console.log('   Dashboard started.');

    // 3. Connect Socket.IO client
    console.log('3. Connecting Socket.IO client...');
    const socket = io(`http://localhost:${DASHBOARD_PORT}`);

    await new Promise((resolve, reject) => {
        socket.on('connect', () => {
            console.log('   Socket.IO client connected.');
            resolve();
        });
        socket.on('connect_error', (err) => {
            console.error('   Socket.IO connection error:', err.message);
            reject(err);
        });
        setTimeout(() => reject(new Error('Socket.IO connection timeout')), 10000);
    });

    // 4. Start log stream for the test log file
    console.log(`4. Starting log stream for ${path.basename(TEST_LOG_FILE)}...`);
    let receivedLines = [];
    socket.on('log-stream-init', (data) => {
        receivedLines = data.lines;
        console.log(`   Received initial ${data.lines.length} lines.`);
    });
    socket.on('log-stream-update', (data) => {
        receivedLines.push(...data.lines);
        console.log(`   Received ${data.lines.length} new lines. Total: ${receivedLines.length}`);
    });
    socket.on('log-stream-error', (error) => {
        console.error('   Log stream error:', error);
    });

    socket.emit('start-log-stream', path.basename(TEST_LOG_FILE));
    await new Promise(resolve => setTimeout(resolve, 1000)); // Give time for stream to start
    console.log('   Log stream initiated.');

    // 5. Write some initial logs
    console.log('5. Writing initial logs...');
    await writeLog('Test log entry 1');
    await writeLog('Test log entry 2');
    await writeLog('Test log entry 3');
    await new Promise(resolve => setTimeout(resolve, 2000)); // Give time for watcher to pick up
    console.log(`   Expected 3 lines. Received: ${receivedLines.length}`);
    console.assert(receivedLines.length >= 3, 'Initial logs not received correctly.');

    // 6. Write more logs to test real-time updates
    console.log('6. Writing more logs for real-time update test...');
    await writeLog('Real-time log entry 4');
    await writeLog('Real-time log entry 5');
    await new Promise(resolve => setTimeout(resolve, 2000));
    console.log(`   Expected at least 5 lines. Received: ${receivedLines.length}`);
    console.assert(receivedLines.length >= 5, 'Real-time updates not received correctly.');

    // 7. Test log rotation/truncation simulation
    console.log('7. Simulating log rotation/truncation...');
    await clearLogFile(); // Simulate truncation
    await writeLog('Log after truncation 1');
    await writeLog('Log after truncation 2');
    await new Promise(resolve => setTimeout(resolve, 3000)); // Give time for watcher to pick up and re-init
    console.log(`   Expected at least 2 lines after truncation. Received: ${receivedLines.length}`);
    // Note: receivedLines will accumulate, so we check if the new lines are present
    console.assert(receivedLines.some(line => line.message?.includes('after truncation 1')), 'Truncated logs not received.');

    // 8. Test API tail endpoint directly
    console.log('8. Testing /api/logs/:filename/tail endpoint directly...');
    const apiTailResponse = await axios.get(`http://localhost:${DASHBOARD_PORT}/api/logs/${path.basename(TEST_LOG_FILE)}?lines=2`);
    console.log(`   API Tail received ${apiTailResponse.data.length} lines.`);
    console.assert(apiTailResponse.data.length === 2, 'API tail did not return correct number of lines.');
    console.assert(apiTailResponse.data[0].message.includes('after truncation 1'), 'API tail content incorrect.');

    // 9. Clean up
    console.log('9. Cleaning up...');
    socket.emit('stop-log-stream');
    socket.disconnect();
    dashboardProcess.kill();
    await clearLogFile();
    console.log('   Done.');

    console.log('\n--- Log Streaming Test Completed Successfully! ---');
    process.exit(0);
}

runTest().catch(async (error) => {
    console.error('\n--- Log Streaming Test Failed! ---');
    console.error(error);
    // Attempt to kill dashboard process if it's still running
    try {
        const axios = require('axios');
        await axios.get(`http://localhost:${DASHBOARD_PORT}/api/health`); // Check if still alive
        const { spawn } = require('child_process');
        spawn('kill', ['-', dashboardProcess.pid], { stdio: 'inherit' }); // Kill process group
    } catch (e) {
        // Process already dead
    }
    process.exit(1);
});
