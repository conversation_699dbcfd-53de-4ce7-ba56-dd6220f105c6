// test-chrome-fix.js - A simple test script to verify Chrome handling
// This script requires puppeteer to be installed: npm install puppeteer
const puppeteer = require('puppeteer');
const { cleanupChromeSession } = require('./utils/chrome-utils');
const { config } = require('./config');

// Handle unhandled promise rejections
process.on('unhandledRejection', (reason, promise) => {
  console.error('Unhandled Rejection at:', promise, 'reason:', reason);
});

// Simple test to launch Chrome, visit a page, and then clean up
async function testChromeFix() {
  console.log('Starting Chrome fix test...');

  // First, clean up any existing Chrome sessions
  console.log('Cleaning up existing Chrome sessions...');
  await cleanupChromeSession();
  console.log('Cleanup completed');

  // Launch a new browser instance with our configuration
  console.log('Launching browser with test configuration...');
  const browser = await puppeteer.launch({
    headless: 'new',
    args: [
      '--no-sandbox',
      '--disable-setuid-sandbox',
      '--disable-dev-shm-usage',
      '--disable-accelerated-2d-canvas',
      '--disable-gpu',
      '--window-size=1280,800',
      '--disable-web-security',
      '--ignore-certificate-errors',
      '--disable-features=IsolateOrigins,site-per-process',
      '--disable-site-isolation-trials',
      '--disable-extensions',
      '--disable-component-extensions-with-background-pages',
      '--disable-default-apps'
    ]
  });

  console.log('Browser launched successfully');

  try {
    // Open a new page and navigate to a test URL
    const page = await browser.newPage();
    console.log('Opening test page...');
    await page.goto('https://www.google.com');
    console.log('Test page loaded successfully');

    // Take a screenshot as evidence
    await page.screenshot({ path: 'test-screenshot.png' });
    console.log('Screenshot saved to test-screenshot.png');

    // Wait a moment
    console.log('Waiting 5 seconds...');
    await new Promise(resolve => setTimeout(resolve, 5000));

    // Close the browser properly
    console.log('Closing browser...');
    await browser.close();
    console.log('Browser closed successfully');

    // Clean up again
    console.log('Running final cleanup...');
    await cleanupChromeSession();
    console.log('Final cleanup completed');

    console.log('Test completed successfully!');
    console.log('Your Chrome browser should still be working normally.');
    console.log('Please verify that your regular Chrome browser is still functional.');
  } catch (error) {
    console.error('Error during test:', error);

    // Try to close the browser if it's still open
    try {
      if (browser) await browser.close();
    } catch (closeError) {
      console.error('Error closing browser:', closeError);
    }

    // Run cleanup in case of error
    await cleanupChromeSession();
  }
}

// Run the test
testChromeFix();
