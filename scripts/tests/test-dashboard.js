// test-dashboard.js - Test the dashboard without starting the full application
const dashboard = require('./dashboard');

console.log('Starting dashboard server...');

// Create a mock state for testing
const configModule = require('./config');
const config = configModule.config;
const state = configModule.state;

// Add some test data to state
state.notifiedLaunches = new Set(['launch1', 'launch2']);
state.launchesCache.data = [
    { id: 'launch1', name: 'Test Launch 1', webcast_live: true },
    { id: 'launch2', name: 'Test Launch 2', webcast_live: false },
    { id: 'launch3', name: 'Test Launch 3', webcast_live: true }
];
state.launchesCache.timestamp = Date.now();

// Start the dashboard
dashboard.startDashboard();

console.log('Dashboard running at http://localhost:3030');
console.log('Press Ctrl+C to stop');

// Simulate some message stats
setTimeout(() => {
    console.log('Simulating successful message...');
    dashboard.recordMessageSent(true);
}, 5000);

setTimeout(() => {
    console.log('Simulating failed message...');
    dashboard.recordMessageSent(false);
}, 10000);

// Keep the process running
process.stdin.resume();
