const { expect } = require('chai');
const sinon = require('sinon');

describe('message-templates.js', () => {
    let sandbox;
    let mockMoment;
    let mockHelpers;
    let mockLanguageManager;
    let mockDefaultConfig;
    let mockI18nLoader;
    let mockConfig;
    let messageTemplates;

    beforeEach(() => {
        sandbox = sinon.createSandbox();

        // Mock moment
        mockMoment = sandbox.stub();
        mockMoment.returns({
            format: sandbox.stub(),
            diff: sandbox.stub(),
            add: sandbox.stub(),
            subtract: sandbox.stub(),
            toDate: sandbox.stub()
        });
        mockMoment.utc = sandbox.stub().returns(mockMoment());
        mockMoment.unix = sandbox.stub().returns(mockMoment());
        mockMoment.isBetween = sandbox.stub().returns(true);
        mockMoment.valueOf = sandbox.stub().returns(12345);

        // Mock helpers
        mockHelpers = {
            hasLivestream: sandbox.stub(),
            getLivestreamUrls: sandbox.stub()
        };

        // Mock languageManager
        mockLanguageManager = {
            getLanguagePreference: sandbox.stub().returns('en'),
            getCommandResponse: sandbox.stub()
        };

        // Mock defaultConfig
        mockDefaultConfig = {
            PROVIDER_EMOJI_MAP: {
                'United Launch Alliance': '🔶', // More specific before less specific
                ULA: '✨',
                SpaceX: '🚀',
                'Rocket Lab': '🔶',
                default: '✨'
            },
            PLATFORM_IDENTIFIERS: [],
            FORMATTING_CONFIG: { maxLengths: { description: 200 } }
        };

        // Mock i18nLoader
        mockI18nLoader = {
            getTemplate: sandbox.stub().returns({
                header: sinon.stub(),
                mission: sinon.stub(),
                vehicle: sinon.stub(),
                provider: sinon.stub(),
                launchSite: sinon.stub(),
                location: sinon.stub(),
                launchTime: sinon.stub(),
                missionOverview: sinon.stub(),
                missionPatch: sinon.stub(),
                destinationOrbit: sinon.stub(),
                payloads: sinon.stub(),
                payloadItem: sinon.stub(),
                webcastHeader: sinon.stub(),
                webcastWatch: sinon.stub(),
                webcastConfirmed: sinon.stub(),
                webcastUnavailable: sinon.stub(),
                footer: sinon.stub(),
                upcomingLaunchEntry: sinon.stub(),
                launchStatusLegend: sinon.stub(),
                noLaunches: sinon.stub(),
                default: sinon.stub()
            }),
            reloadLanguages: sandbox.stub().returns(true)
        };

        // Mock config
        mockConfig = {
            language: { multiLang: false, default: 'en' },
            dailyDigest: { lookAheadDays: 1 },
            images: { enabled: true }
        };

        // Load the module with mocked dependencies
        messageTemplates = require('../utils/message-templates')(mockConfig, {
            moment: mockMoment,
            hasLivestream: mockHelpers.hasLivestream,
            getLivestreamUrls: mockHelpers.getLivestreamUrls,
            languageManager: mockLanguageManager,
            defaultConfig: mockDefaultConfig,
            i18nLoader: mockI18nLoader
        });
    });

    afterEach(() => {
        sandbox.restore();
    });

    describe('getProviderEmoji', () => {
        it('should return the correct emoji for a known provider', () => {
            const emoji = messageTemplates.getProviderEmoji('SpaceX');
            expect(emoji).to.equal('🚀');
        });

        it('should return the default emoji for an unknown provider', () => {
            const emoji = messageTemplates.getProviderEmoji('Unknown Corp');
            expect(emoji).to.equal('✨');
        });

        it('should return the correct emoji for a provider with partial match', () => {
            const emoji = messageTemplates.getProviderEmoji('United Launch Alliance');
            expect(emoji).to.equal('🔶');
        });
    });

    // Add more describe blocks and tests for other functions as needed
});