const { expect } = require('chai');
const sinon = require('sinon');

describe('config-loader.js', () => {
    let sandbox;
    let mockFs;
    let mockPath;
    let mockYaml;
    let mockDotenv;
    let mockProcessEnv;
    let mockLogger;
    let mockPackageJson;

    beforeEach(() => {
        sandbox = sinon.createSandbox();

        // Mock dependencies
        mockFs = {
            existsSync: sandbox.stub().returns(false),
            readFileSync: sandbox.stub().returns('')
        };
        mockPath = {
            resolve: sandbox.stub().callsFake((...args) => {
                if (args.length === 2 && args[1] === '.env') return '/mock/path/.env';
                if (args.length === 2 && args[1] === 'config.yaml') return '/mock/path/config.yaml';
                if (args.length === 2 && args[1] === 'config.dev.yaml') return '/mock/path/config.dev.yaml';
                if (args.length === 2 && args[1] === '/data/options.json') return '/data/options.json';
                return args.join('/'); // Simplified path joining for mocks
            }),
            join: sandbox.stub().callsFake((...args) => args.join('/'))
        };
        mockYaml = {
            load: sandbox.stub()
        };
        mockDotenv = {
            config: sandbox.stub()
        };
        mockProcessEnv = {
            env: {},
            cwd: sandbox.stub().returns('/mock/cwd')
        };
        mockLogger = {
            info: sandbox.stub(),
            warn: sandbox.stub(),
            error: sandbox.stub()
        };
        mockPackageJson = {
            version: '1.0.0'
        };

        // Suppress console output from the module being tested
        sandbox.stub(console, 'log');
        sandbox.stub(console, 'warn');
        sandbox.stub(console, 'error');
    });

    afterEach(() => {
        sandbox.restore(); // Restore all stubs
    });

    it('should load config.yaml when no SLN_ENV and no options.json', () => {
        // Arrange
        mockProcessEnv.env.SLN_ENV = ''; // No specific environment
        mockProcessEnv.env.SLN_HA_CONFIG_PATH = ''; // No HA config path

        mockFs.existsSync.withArgs('/mock/path/config.yaml').returns(true);
        mockFs.readFileSync.withArgs('/mock/path/config.yaml').returns('whatsapp:\n  sessionName: test-session\napi:\n  maxResults: 5');
        mockYaml.load.returns({
            whatsapp: {
                sessionName: 'test-session'
            },
            api: {
                maxResults: 5
            }
        });

        // Act
        const configLoader = require('../utils/config-loader');
        const { config } = configLoader({
            fs: mockFs,
            path: mockPath,
            yaml: mockYaml,
            dotenv: mockDotenv,
            processEnv: mockProcessEnv,
            logger: mockLogger,
            packageJson: mockPackageJson
        });

        // Assert
        expect(config.whatsapp.sessionName).to.equal('test-session');
        expect(config.api.maxResults).to.equal(5);
        // Ensure default values are merged
        expect(config.dailyDigest.enabled).to.be.true;
        expect(config.debug).to.be.false; // Default debug is false
    });

    it('should load options.json and map whatsapp_group_id and debug', () => {
        // Arrange
        mockProcessEnv.env.SLN_ENV = '';
        mockProcessEnv.env.SLN_HA_CONFIG_PATH = '/data/options.json';

        mockFs.existsSync.withArgs('/data/options.json').returns(true);
        mockFs.readFileSync.withArgs('/data/options.json').returns(JSON.stringify({
            whatsapp_group_id: '<EMAIL>',
            debug: true,
            some_other_option: 'value'
        }));

        // Act
        const configLoader = require('../utils/config-loader');
        const { config } = configLoader({
            fs: mockFs,
            path: mockPath,
            yaml: mockYaml,
            dotenv: mockDotenv,
            processEnv: mockProcessEnv,
            logger: mockLogger,
            packageJson: mockPackageJson
        });

        // Assert
        expect(config.whatsapp.groupId).to.equal('<EMAIL>');
        expect(config.debug).to.be.true;
        // Ensure other default values are still present
        expect(config.api.launchApiUrl).to.exist;
        expect(config.whatsapp.sessionName).to.equal('launch-notifier'); // Default value
    });

    it('should load config.dev.yaml when SLN_ENV is DEV', () => {
        // Arrange
        mockProcessEnv.env.SLN_ENV = 'DEV';
        mockProcessEnv.env.SLN_HA_CONFIG_PATH = ''; // Ensure HA path doesn't interfere

        mockFs.existsSync.withArgs('/mock/path/config.dev.yaml').returns(true);
        mockFs.readFileSync.withArgs('/mock/path/config.dev.yaml').returns('whatsapp:\n  sessionName: dev-session\ndebug: true');
        mockYaml.load.returns({
            whatsapp: {
                sessionName: 'dev-session'
            },
            debug: true
        });

        // Act
        const configLoader = require('../utils/config-loader');
        const { config } = configLoader({
            fs: mockFs,
            path: mockPath,
            yaml: mockYaml,
            dotenv: mockDotenv,
            processEnv: mockProcessEnv,
            logger: mockLogger,
            packageJson: mockPackageJson
        });

        // Assert
        expect(config.whatsapp.sessionName).to.equal('dev-session');
        expect(config.debug).to.be.true;
        expect(config.dailyDigest.enabled).to.be.true; // Default value
    });

    it('should use default hardcoded values if no config file is found', () => {
        // Arrange
        mockProcessEnv.env.SLN_ENV = '';
        mockProcessEnv.env.SLN_HA_CONFIG_PATH = '';
        mockFs.existsSync.returns(false); // Ensure no config files exist

        // Act
        const configLoader = require('../utils/config-loader');
        const { config } = configLoader({
            fs: mockFs,
            path: mockPath,
            yaml: mockYaml,
            dotenv: mockDotenv,
            processEnv: mockProcessEnv,
            logger: mockLogger,
            packageJson: mockPackageJson
        });

        // Assert
        expect(config.whatsapp.sessionName).to.equal('launch-notifier'); // Default value
        expect(config.api.launchApiUrl).to.exist; // Default value
        expect(config.debug).to.be.false; // Default value
    });
});