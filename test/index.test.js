const { expect } = require('chai');
const sinon = require('sinon');
const path = require('path');
const proxyquire = require('proxyquire');

// Mock external modules to prevent actual side effects during testing
const mockConfig = {
    version: 'test-version',
    whatsapp: {
        connectionRetry: {
            initWait: 100
        },
        sessionName: 'test-session'
    },
    paths: {
        tokensDir: './tokens'
    },
    debug: false
};

// Directly define the mockLogger object to contain the stubbed getLogger method
const mockLogger = {
    getLogger: sinon.stub(),
    initializeLogger: sinon.stub(),
    isHomeAssistant: sinon.stub().returns(false)
};

const mockMessaging = {
    connectToWhatsApp: sinon.stub().resolves(),
    closeWhatsAppConnection: sinon.stub().resolves(),
    sendGroupMessage: sinon.stub().resolves(true)
};

const mockLaunchMonitor = sinon.stub();

const mockDailyDigest = sinon.stub();

const mockTemplateWatcher = sinon.stub();

const mockProgress = {
    createSpinner: sinon.stub().returns({
        update: sinon.stub(),
        stop: sinon.stub()
    }),
    createProgressBar: sinon.stub().returns({
        update: sinon.stub(),
        complete: sinon.stub()
    }),
    createCountdown: sinon.stub().returns({
        stop: sinon.stub()
    })
};

const mockDashboard = sinon.stub();

const mockConnectionMonitor = sinon.stub();

const mockUrlTracker = {};
const mockApi = {
    getLatestLaunchData: sinon.stub().resolves([])
};
const mockHelpers = {
    hasLivestream: sinon.stub(),
    getLivestreamUrls: sinon.stub()
};
const mockLanguageManager = {};

// Mocks for config-loader dependencies
const mockProcessEnv = {
    env: {},
    cwd: sinon.stub().returns('/Users/<USER>/code/space-launch-notifier')
};
const mockFs = {
    existsSync: sinon.stub(),
    readFileSync: sinon.stub()
};
const mockYaml = {
    load: sinon.stub()
};
const mockDotenv = {
    config: sinon.stub()
};
const mockPackageJson = {
    version: '1.0.0'
};

// Mock defaultConfig to include PROVIDER_EMOJI_MAP
const mockDefaultConfig = {
    PROVIDER_EMOJI_MAP: {
        'United Launch Alliance': '🔶', // More specific before less specific
        ULA: '✨',
        SpaceX: '🚀',
        'Rocket Lab': '🔶',
        default: '✨'
    }
};

const mockI18nLoader = {};
const mockMoment = sinon.stub().returns({
    format: sinon.stub().returns('mock-date')
});
const mockSchedule = {
    scheduleJob: sinon.stub().returns({ cancel: sinon.stub() })
};
const mockSpacePeople = {};
const mockUpdateHandler = {};

// Mock messageTemplates to simulate its behavior
const mockMessageTemplates = {
    formatStatusMessage: sinon.stub().returns('Mock status message'),
    getProviderEmoji: sinon.stub().callsFake(providerName => {
        const matchedProvider = Object.keys(mockDefaultConfig.PROVIDER_EMOJI_MAP).find(
            provider => providerName.includes(provider)
        );
        return matchedProvider ?
            mockDefaultConfig.PROVIDER_EMOJI_MAP[matchedProvider] :
            mockDefaultConfig.PROVIDER_EMOJI_MAP.default;
    })
};

// Mock process.on to control event listeners
let processOnStub;


describe('index.js', () => {
    let processExitStub;
    let consoleErrorStub;
    let startApplication;
    let gracefulShutdown;
    let currentLoggerInstance;

    beforeEach(() => {
        console.log('beforeEach start');
        // Reset all stubs before each test
        sinon.resetHistory();

        // Stub process.exit, console.error, and process.on
        processExitStub = sinon.stub(process, 'exit');
        consoleErrorStub = sinon.stub(console, 'error');
        processOnStub = sinon.stub(process, 'on');

        // Create a fresh logger instance for each test
        currentLoggerInstance = {
            info: sinon.stub(),
            error: sinon.stub(),
            debug: sinon.stub(),
            warn: sinon.stub()
        };
        mockLogger.getLogger.returns(currentLoggerInstance);

        // Set the return value for mockConnectionMonitor
        const connectionMonitorInstance = {
            startConnectionMonitor: sinon.stub(),
            checkConnection: sinon.stub(),
            attemptReconnect: sinon.stub()
        };
        mockConnectionMonitor.returns(connectionMonitorInstance);

        // Set the return value for mockTemplateWatcher
        const templateWatcherInstance = {
            startWatching: sinon.stub()
        };
        mockTemplateWatcher.returns(templateWatcherInstance);

        // Set the return value for mockLaunchMonitor
        const launchMonitorInstance = {
            startLaunchMonitor: sinon.stub()
        };
        mockLaunchMonitor.returns(launchMonitorInstance);

        // Set the return value for mockDailyDigest
        const dailyDigestInstance = {
            scheduleDailyDigest: sinon.stub().callsFake(() => {
                // Simulate the synchronous part of scheduleDailyDigest
                return { cancel: sinon.stub() }; // Return a mock job object
            })
        };
        mockDailyDigest.returns(dailyDigestInstance);

        // Set the return value for mockDashboard
        const dashboardInstance = {
            startDashboard: sinon.stub().resolves({ port: 3030 }),
            recordMessageSent: sinon.stub()
        };
        mockDashboard.returns(dashboardInstance);
        console.log('dashboardInstance.startDashboard is stub:', dashboardInstance.startDashboard.isSinonProxy);

        // Clear the module cache for dashboard before using proxyquire
        delete require.cache[require.resolve('../dashboard.js')];

        // Use proxyquire to load index.js with mocked dependencies
        const indexModule = proxyquire('../index.js', {
            './utils/logger': mockLogger, // Pass the mockLogger directly
            './utils/messaging': sinon.stub().returns(mockMessaging),
            './launch-monitor': sinon.stub().returns(mockLaunchMonitor),
            './daily-digest': sinon.stub().returns(mockDailyDigest),
            './utils/template-watcher': sinon.stub().returns(mockTemplateWatcher),
            './utils/progress-indicator': mockProgress,
                                    './dashboard': dashboardInstance,
            './utils/connection-monitor': mockConnectionMonitor,
            './utils/url-tracker': mockUrlTracker,
            './utils/api': sinon.stub().returns(mockApi),
            './utils/helpers': mockHelpers,
            './utils/language-manager': mockLanguageManager,
            './utils/default-config': mockDefaultConfig,
            './utils/i18n/loader': mockI18nLoader,
            'moment': mockMoment,
            'node-schedule': mockSchedule,
            './utils/space-people': mockSpacePeople,
            './utils/update-handler': sinon.stub().returns(mockUpdateHandler),
            './utils/message-templates': sinon.stub().returns(mockMessageTemplates)
        });

        // Call the exported function from index.js with mock config and state
        const app = indexModule({ config: mockConfig, state: { scheduledJobs: [] } });
        startApplication = app.startApplication;
        gracefulShutdown = app.gracefulShutdown;

        // Initialize the logger with the mock config
        mockLogger.initializeLogger(mockConfig);
        console.log('beforeEach end');

        // Store the connectionMonitorInstance for assertions
        this.connectionMonitorInstance = connectionMonitorInstance;

        // Store the templateWatcherInstance for assertions
        this.templateWatcherInstance = templateWatcherInstance;

        // Store the launchMonitorInstance for assertions
        this.launchMonitorInstance = launchMonitorInstance;

        // Store the dailyDigestInstance for assertions
        this.dailyDigestInstance = dailyDigestInstance;

        // Store the dashboardInstance for assertions
        this.dashboardInstance = dashboardInstance;
    });

    afterEach(() => {
        console.log('afterEach start');
        // Restore original functions after each test
        processExitStub.restore();
        consoleErrorStub.restore();
        processOnStub.restore();
        console.log('afterEach end');
    });

    it('should initialize and start the application successfully', async () => {
        await startApplication();

        expect(mockLogger.initializeLogger.calledOnce).to.be.true;
        expect(mockLogger.getLogger.calledWith('main')).to.be.true;
        expect(mockMessaging.connectToWhatsApp.calledOnce).to.be.true;
        expect(this.connectionMonitorInstance.startConnectionMonitor.calledOnce).to.be.true;
        expect(this.templateWatcherInstance.startWatching.calledOnce).to.be.true;
        expect(this.launchMonitorInstance.startLaunchMonitor.calledOnce).to.be.true;
        expect(this.dailyDigestInstance.scheduleDailyDigest.calledOnce).to.be.true;
        expect(this.dashboardInstance.startDashboard.calledOnce).to.be.true;
        expect(currentLoggerInstance.info.calledWith('All systems initialized!')).to.be.true;
        expect(processExitStub.notCalled).to.be.true;
    }).timeout(5000); // Increase timeout for this test

    it('should handle unhandled promise rejections and exit', async () => {
        const testError = new Error('Test unhandled rejection');
        const testPromise = Promise.reject(testError);

        // Manually call the unhandledRejection handler that is set up in index.js
        const unhandledRejectionHandlerCall = processOnStub.getCalls().find(call => call.args[0] === 'unhandledRejection');
        if (unhandledRejectionHandlerCall) {
            const unhandledRejectionHandler = unhandledRejectionHandlerCall.args[1];
            unhandledRejectionHandler(testError, testPromise);
        } else {
            throw new Error('unhandledRejection handler not found');
        }

        expect(currentLoggerInstance.error.calledWith('Unhandled Rejection at:', testPromise, 'reason:', testError)).to.be.true;
        expect(currentLoggerInstance.error.calledWith('--- SHUTDOWN ---')).to.be.true;
        expect(processExitStub.calledWith(1)).to.be.true;
    });

    it('should handle fatal errors during startup and exit', async () => {
        const startupError = new Error('Failed to connect to WhatsApp');
        mockMessaging.connectToWhatsApp.rejects(startupError);

        await startApplication();

        expect(currentLoggerInstance.error.calledWith(`Fatal error: ${startupError.message}`)).to.be.true;
        expect(processExitStub.calledWith(1)).to.be.true;
    }).timeout(5000);

    it('should call gracefulShutdown on SIGINT', async () => {
        // Call startApplication to set up the SIGINT listener
        await startApplication();

        // Directly call gracefulShutdown
        await gracefulShutdown();

        // Give some time for the async shutdown to complete
        await new Promise(resolve => setTimeout(resolve, mockConfig.whatsapp.connectionRetry.initWait + 500)); // Increased timeout

        expect(mockMessaging.closeWhatsAppConnection.calledOnce).to.be.true;
        expect(processExitStub.calledWith(0)).to.be.true;
    }).timeout(5000);

    it('should call gracefulShutdown on SIGTERM', async () => {
        // Call startApplication to set up the SIGTERM listener
        await startApplication();

        // Directly call gracefulShutdown
        await gracefulShutdown();

        // Give some time for the async shutdown to complete
        await new Promise(resolve => setTimeout(resolve, mockConfig.whatsapp.connectionRetry.initWait + 500)); // Increased timeout

        expect(mockMessaging.closeWhatsAppConnection.calledOnce).to.be.true;
        expect(processExitStub.calledWith(0)).to.be.true;
    }).timeout(5000);
});