const { expect } = require('chai');
const sinon = require('sinon');
const path = require('path');
const fs = require('fs');
const winston = require('winston');

// Custom transport for spying
class SpyTransport extends winston.Transport {
  constructor(options) {
    super(options);
    this.logs = [];
  }

  log(info, callback) {
    this.logs.push(info);
    callback();
  }
}

// Mock the config-loader to control the config object for testing
const mockConfig = {
  debug: false,
  logging: {
    level: 'info',
    console: {
      enabled: true,
      level: 'info',
      colorize: true
    },
    file: {
      enabled: true,
      level: 'info',
      maxSize: '20m',
      maxFiles: '14d',
      dirname: './data/logs'
    }
  },
  paths: {
    tokensDir: './tokens',
    dataDir: './data',
    templatesDir: './utils/templates',
    logsDir: './data/logs'
  },
  whatsapp: {
    sessionName: 'test-session'
  }
};

let loggerModule;
let spyTransport;

describe('logger.js', () => {
  beforeEach(() => {
    // Clear require cache for logger and config-loader to ensure fresh imports
    delete require.cache[require.resolve('../utils/logger')];
    delete require.cache[require.resolve('../utils/config-loader')];

    // Mock fs.existsSync for isHomeAssistant check
    sinon.stub(fs, 'existsSync').returns(false); // Assume not Home Assistant by default

    // Mock fs.mkdirSync to prevent actual directory creation during tests
    sinon.stub(fs, 'mkdirSync').returns(true);

    // Mock fs.promises.readdir and fs.promises.unlink for clearLogs
    sinon.stub(fs.promises, 'readdir').resolves(['app-2025-08-02.log', 'error-2025-08-02.log']);
    sinon.stub(fs.promises, 'unlink').resolves();

    // Re-require the logger module and initialize it
    loggerModule = require('../utils/logger');
    spyTransport = new SpyTransport();
    loggerModule.initializeLogger(mockConfig, spyTransport);

    // Clear logs from previous tests
    spyTransport.logs = [];

    // Reset mockConfig.debug and logging level for each test
    mockConfig.debug = false;
    mockConfig.logging.level = 'info';
  });

  afterEach(() => {
    sinon.restore(); // Restore all stubs and spies
  });

  it('should initialize logger with provided config', () => {
    const log = loggerModule.getLogger('test');
    expect(log).to.exist;
    expect(log.info).to.be.a('function');
    expect(log.error).to.be.a('function');
  });

  it('should log info messages to console', () => {
    const log = loggerModule.getLogger('test');
    log.info('This is an info message');
    const infoLog = spyTransport.logs.find(log => log.message === 'This is an info message');
    expect(infoLog).to.exist;
    expect(infoLog.level).to.equal('info');
  });

  it('should log error messages to console', () => {
    const log = loggerModule.getLogger('test');
    log.error('This is an error message');
    const errorLog = spyTransport.logs.find(log => log.message === 'This is an error message');
    expect(errorLog).to.exist;
    expect(errorLog.level).to.equal('error');
  });

  it('should enable debug logging when config.debug is true', () => {
    mockConfig.debug = true;
    mockConfig.logging.level = 'debug'; // Ensure logger level is debug
    // Re-initialize logger with debug enabled
    delete require.cache[require.resolve('../utils/logger')];
    loggerModule = require('../utils/logger');
    spyTransport = new SpyTransport();
    loggerModule.initializeLogger(mockConfig, spyTransport);

    const log = loggerModule.getLogger('debugTest');
    log.debug('This is a debug message');
    const debugLog = spyTransport.logs.find(log => log.message === 'This is a debug message');
    expect(debugLog).to.exist;
    expect(debugLog.level).to.equal('debug');
  });

  it('should not log debug messages when config.debug is false', () => {
    mockConfig.debug = false;
    // Re-initialize logger with debug disabled
    delete require.cache[require.resolve('../utils/logger')];
    loggerModule = require('../utils/logger');
    spyTransport = new SpyTransport();
    loggerModule.initializeLogger(mockConfig, spyTransport);

    const log = loggerModule.getLogger('noDebugTest');
    log.debug('This message should not appear');
    const debugLog = spyTransport.logs.find(log => log.level === 'debug');
    expect(debugLog).to.not.exist;
  });

  it('should return the correct log directory', () => {
    const logDir = loggerModule.getLogDir();
    expect(logDir).to.include(path.join('data', 'logs'));
  });

  it('should return log files', async () => {
    const logFiles = await loggerModule.getLogFiles();
    expect(logFiles).to.be.an('array').that.is.not.empty;
    expect(logFiles[0]).to.have.property('name', 'app-2025-08-02.log');
  });

  it('should clear log files', async () => {
    const result = await loggerModule.clearLogs();
    expect(result).to.be.true;
    expect(fs.promises.unlink.calledTwice).to.be.true; // Called for each mocked log file
    expect(spyTransport.logs.some(log => log.message.includes('Deleted log file: app-2025-08-02.log'))).to.be.true;
    expect(spyTransport.logs.some(log => log.message.includes('All log files cleared successfully.'))).to.be.true;
  });

  it('should detect Home Assistant environment correctly', () => {
    fs.existsSync.withArgs('/data/options.json').returns(true);
    expect(loggerModule.isHomeAssistant()).to.be.true;
  });

  it('should not detect Home Assistant environment if paths are missing', () => {
    fs.existsSync.withArgs('/data/options.json').returns(false);
    expect(loggerModule.isHomeAssistant()).to.be.false;
  });
});
