FROM node:18-alpine

# Install system dependencies for development
RUN     apk add      chromium         nss         freetype         harfbuzz         ca-certificates         ttf-freefont         fontconfig         dbus         udev         bash         git         curl

RUN curl -J -L -o /tmp/bashio.tar.gz "https://github.com/hassio-addons/bashio/archive/v0.16.2.tar.gz" && \
    mkdir -p /tmp/bashio && \
    tar zxvf /tmp/bashio.tar.gz --strip 1 -C /tmp/bashio && \
    mv /tmp/bashio/lib /usr/lib/bashio && \
    ln -s /usr/lib/bashio/bashio /usr/bin/bashio && \
    rm -rf /tmp/bashio.tar.gz /tmp/bashio

# Set working directory
WORKDIR /usr/src/app

# Copy package.json and package-lock.json
COPY package.json package-lock.json ./

# Install ALL dependencies (including dev)
RUN npm install

# Copy entire project
COPY . .

# Create necessary directories
RUN mkdir -p /data/tokens /data/logs

# Set environment variables for Puppeteer
ENV PUPPETEER_SKIP_CHROMIUM_DOWNLOAD=true \
    PUPPETEER_EXECUTABLE_PATH=/usr/bin/chromium-browser \
    PUPPETEER_ARGS="--no-sandbox,--disable-setuid-sandbox,--disable-dev-shm-usage,--disable-accelerated-2d-canvas,--disable-gpu,--window-size=1920x1080"

# Set environment variables for development
ENV TOKENS_DIR="/data/tokens" \
    DATA_DIR="/data" \
    TEMPLATES_DIR="/usr/src/app/utils/templates" \
    LOG_FILE_DIR="/data/logs" \
    NODE_ENV="development" \
    DEBUG=true \
    LOG_LEVEL=debug

# Expose the web interface port
EXPOSE 3033

# Set up entry point
COPY run.sh /
RUN chmod a+x /run.sh
CMD [ "/run.sh" ]
