ARG BUILD_FROM=ghcr.io/home-assistant/amd64-base:3.19

# --- Builder Stage ---
FROM ${BUILD_FROM} AS builder

# Set shell
SHELL ["/bin/bash", "-o", "pipefail", "-c"]

# Install build dependencies
RUN apk add --no-cache nodejs npm

# Set working directory
WORKDIR /usr/src/app

# Copy package files and install all dependencies
COPY package.json package-lock.json ./
COPY run.sh ./
RUN npm ci --omit=dev

COPY . .


# --- Production Stage ---
FROM ${BUILD_FROM}

# Set shell
SHELL ["/bin/bash", "-o", "pipefail", "-c"]

# Set build version
ARG BUILD_VERSION=2025.08.04.8dd6baeb

# Install production dependencies
RUN apk add --no-cache nodejs chromium nss freetype harfbuzz ca-certificates ttf-freefont fontconfig dbus udev bash jq



# Set working directory
WORKDIR /usr/src/app

# Copy dependencies from builder stage
COPY --from=builder /usr/src/app/node_modules ./node_modules

# Copy application files
COPY --from=builder /usr/src/app .
COPY --from=builder /usr/src/app/run.sh /run.sh

# Create necessary directories
RUN mkdir -p /data/tokens /data/logs && \
    chmod 777 /data/tokens /data/logs

# Set environment variables for Puppeteer
ENV PUPPETEER_SKIP_CHROMIUM_DOWNLOAD=true \
    PUPPETEER_EXECUTABLE_PATH=/usr/bin/chromium-browser \
    PUPPETEER_ARGS="--no-sandbox,--disable-setuid-sandbox,--disable-dev-shm-usage,--disable-accelerated-2d-canvas,--disable-gpu,--window-size=1920x1080"

# Set environment variables for the application
ENV TOKENS_DIR="/data/tokens" \
    DATA_DIR="/data" \
    TEMPLATES_DIR="/usr/src/app/utils/templates" \
    LOG_FILE_DIR="/data/logs" \
    NODE_ENV="production"

# Expose the web interface port
EXPOSE 3030

# Set up entry point

RUN chmod a+x /run.sh
CMD ["/run.sh"]

# Labels
LABEL \
    io.hass.name="Space Launch Notifier" \
    io.hass.description="Get WhatsApp notifications for upcoming rocket launches with livestreams" \
    io.hass.type="addon" \
    io.hass.version="${BUILD_VERSION}" \
    maintainer="Keni Barwick <<EMAIL>>"
