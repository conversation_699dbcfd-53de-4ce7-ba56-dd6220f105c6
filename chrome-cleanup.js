
const fs = require('fs');
const path = require('path');
const config = require('./config').config;

const dir = path.join(__dirname, config.paths.tokensDir, config.whatsapp.sessionName);

if (fs.existsSync(dir)) {
  ['SingletonLock', 'SingletonCookie', 'SingletonSocket', 'DevToolsActivePort'].forEach(f => {
    const p = path.join(dir, f);
    if (fs.existsSync(p)) {
      fs.unlinkSync(p);
      console.log('Removed ' + f);
    }
  });
}
