// start.js - Clean startup script for Space Launch Notifier

const configLoaderFactory = require('./utils/config-loader');
const mainApp = require('./index'); // The main application logic

// Mock dependencies for config-loader for local testing
const mockLogger = {
    info: (...args) => console.log('[MockLogger INFO]', ...args),
    warn: (...args) => console.warn('[MockLogger WARN]', ...args),
    error: (...args) => console.error('[MockLogger ERROR]', ...args),
    debug: (...args) => console.debug('[MockLogger DEBUG]', ...args),
};
const mockFs = require('fs');
const mockPath = require('path');
const mockYaml = require('js-yaml');
const mockDotenv = require('dotenv');
const mockPackageJson = require('./package.json');

const configLoader = configLoaderFactory({
    logger: mockLogger,
    processEnv: process,
    fs: mockFs,
    path: mockPath,
    yaml: mockYaml,
    dotenv: mockDotenv,
    packageJson: mockPackageJson
});

async function start() {
    console.log('Preparing to start Space Launch Notifier...');

    try {
        // Load configuration and initialize state
        const { config, state } = configLoader;

        // Initialize and start the main application
        const { startApplication, gracefulShutdown } = mainApp({ config, state });

        // Start the application
        await startApplication();

        // Handle signals for graceful shutdown
        process.on('SIGINT', gracefulShutdown);
        process.on('SIGTERM', gracefulShutdown);

    } catch (error) {
        console.error('Fatal error during startup:', error);
        process.exit(1);
    }
}

// Run the start function
start();