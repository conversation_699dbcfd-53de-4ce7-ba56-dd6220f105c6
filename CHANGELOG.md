## [2025.08.04.8dd6baeb] - 2025-08-04 No significant changes.\n\n### Changed\n\n- No new changes to report.\n\n
## [2025.08.04.21fd2b17] - 2025-08-04 No significant changes.\n\n### Changed\n\n- No new changes to report.\n\n
## [2025.08.04.7972ef70] - 2025-08-04 No significant changes.\n\n### Changed\n\n- No new changes to report.\n\n
## [2025.08.04.06195238] - 2025-08-04 No significant changes.\n\n### Changed\n\n- No new changes to report.\n\n
## [2025.08.04.18f42300] - 2025-08-04 No significant changes.\n\n### Changed\n\n- No new changes to report.\n\n
## [2025.08.04.8a509c82] - 2025-08-04 No significant changes.\n\n### Changed\n\n- No new changes to report.\n\n
## [2025.08.04.a3e43bf1] - 2025-08-04 No significant changes.\n\n### Changed\n\n- No new changes to report.\n\n
## [2025.08.04.a0faaa51] - 2025-08-04 No significant changes.\n\n### Changed\n\n- No new changes to report.\n\n
## [2025.08.04.802e9735] - 2025-08-04 No significant changes.\n\n### Changed\n\n- No new changes to report.\n\n
## [2025.08.04.e03303ff] - 2025-08-04 chore: Optimize Dockerfile to reduce image size\n\n### Changed\n\n- chore: Optimize Dockerfile to reduce image size\n\n
## [2025.08.04.1899a5ac] - 2025-08-04 chore: Ignore tokens directory\n\n### Changed\n\n- chore: Ignore tokens directory
- chore: Ignore tokens directory in .gitignore
- fix: Resolve startup errors and improve config loading\n\n
## [2025.08.03.406f3c86] - 2025-08-03

### Fixed

- Resolved multiple startup errors related to module initialization and undefined properties.
- Ensured `state.launchesCache` is initialized before use by the API module.
- Corrected API module export to ensure functions are properly exposed.
- Passed `config` object to `cleanupChromeSession` to prevent `TypeError` during Chrome session cleanup.

## [2025.08.03.406f3c85] - 2025-08-03 No significant changes.

### Changed

- No new changes to report.


## [2025.08.03.20df95c0] - 2025-08-03 chore: Add verbose logging to run.sh for startup debugging\n\n### Changed\n\n- chore: Add verbose logging to run.sh for startup debugging
- docs: Update changelog entry\n\n
## [2025.08.03.601dfb0c] - 2025-08-03 No significant changes.\n\n### Changed\n\n- No new changes to report.\n\n
## [2025.08.04] - 2025-08-04

### Changed

- Placeholder for new changes.


## [2025.08.03.92bcf5ff] - 2025-08-03 chore: update changelog entries for consistency

### Changed

- chore: update changelog entries for consistency\n\n
## [2025.08.03.93f3c4b9] - 2025-08-03 fix: resolve startup errors and improve config loading\n\n### Changed\n\n- fix: resolve startup errors and improve config loading
- docs: Add Linkly migration to TODO list
- chore: Remove temporary changelog_entry.txt\n\n
## [2025.08.02.b0b7f7a] - 2025-08-02 chore: Enhance shipit.sh to display test results in summary\n\n### Changed\n\n- chore: Enhance shipit.sh to display test results in summary\n\n
## [2025.08.02.5fe137b] - 2025-08-02 docs: Update README.md with testing info, API examples, and correct versioning in shipit.sh\n\n### Changed\n\n- docs: Update README.md with testing info, API examples, and correct versioning in shipit.sh\n\n
## [2025.08.01.c1dfa15] - 2025-08-01 feat(message-templates): Refactor for testability and add unit tests\n\n### Changed\n\n- feat(message-templates): Refactor for testability and add unit tests\n\n
## [2025.08.01.14889f0] - 2025-08-01 feat(config): Refactor config-loader for testability and add unit tests\n\n### Changed\n\n- feat(config): Refactor config-loader for testability and add unit tests\n\n
## [2025.08.01.772122b] - 2025-08-01 chore(changelog): Reformat existing changelog entries\n\n### 
Changed\n\n- chore(changelog): Reformat existing changelog entries\n\n

## [2025.08.01.1d2e8bd] - 2025-08-01 - Fix: Corrected changelog formatting

### Changed
- fix(shipit): Correct changelog formatting\n\n

## [2025.08.01.7e1210e] - 2025-08-01 - Fix: Ensure people in space is always included in digest

### Changed
- fix(daily-digest): Ensure people in space is always included in digest\n\n
## [2025.08.01.bb6146c] - 2025-08-01 - Fix: Resolve circular dependency with dashboard

### Changed
- fix(messaging): Resolve circular dependency with dashboard\n\n
## [2025.08.01.9c4a89b] - 2025-08-01 - Fix: Correct initialization order for logger and dailyDigest

### Changed
- fix(dashboard): Correct initialization order for logger and dailyDigest\n\n
## [2025.08.01.e698c9a] - 2025-08-01 - Fix: Initialize daily-digest module in dashboard

### Changed
- fix(api): Initialize daily-digest module in dashboard\n\n
## [2025.08.01.a860743] - 2025-08-01 - Feat: Improved error handling and increased Puppeteer timeout


### Changed
- fix(error-handling): Increased Puppeteer protocol timeout to prevent crashes on slow systems.
- feat(error-handling): Added a global error handler to log a shutdown message and exit on unhandled rejections, ensuring the add-on does not become a zombie process.

## [2025.08.01.fbaff9f] - 2025-08-01 - No significant changes.

### Changed
- No new changes to report.\n\n
## [2025.08.01.f68dd05] - 2025-08-01 - No significant changes.

### Changed
- No new changes to report.\n\n
## [2025.08.01.d2c004f] - 2025-08-01 - Chore: Ship it release

### Changed
- chore(release): ship it 2025.08.01.d2c004f
- No new changes to report.\n\n
## [2025.08.01.146ec3f] - 2025-08-01

### Changed
- No new changes to report.


## [2025.08.01.1c3abec] - 2025-08-01

### Changed
- No new changes to report.


## [2025.08.01.1c3abec] - 2025-08-01

### Changed
- No new changes to report.


## [2025.08.01.e5c67ad] - 2025-08-01

### Changed
- No new changes to report.


## [2025.08.01.e5c67ad] - 2025-08-01

### Changed
- No new changes to report.


## [2025.08.01.e31d6a1] - 2025-08-01

### Changed
- No new changes to report.


## [2025.08.01.e31d6a1] - 2025-08-01

### Changed
- Fix: Remove remaining merge conflict markers from package.json
- Fix: Remove remaining merge conflict markers from Dockerfile


## [2025.08.01.8a98eed] - 2025-08-01

### Changed
- No new changes to report.


## [2025.08.01.8a98eed] - 2025-08-01

### Changed
- Fix: Resolve config.yaml merge conflict markers


## [2025.08.01.5469fbb] - 2025-08-01

### Changed
- No new changes to report.


## [2025.08.01.5469fbb] - 2025-08-01

### Changed
- chore(release): ship it 2025.08.01.0964ee8
- chore(release): ship it 2025.07.31.eceec8b


## [2025.08.01.0964ee8] - 2025-08-01

### Changed
- chore(release): ship it 2025.07.31.eceec8b

## [2025.07.31.eceec8b] - 2025-07-31

### Changed
- No new changes to report.


## [2025.07.31.a9e8599] - 2025-07-31

### Changed
- No new changes to report.


## [2025.07.31.ca07627] - 2025-07-31

### Changed
- chore(release): ship it 2025.07.31.f68b253


## [2025.07.31.f68b253] - 2025-07-31

### Changed
- No new changes to report.


## [2025.07.31.ddcb20c] - 2025-07-31

### Changed
- No new changes to report.


## [2025.07.31.9411291] - 2025-07-31

### Changed
- No new changes to report.


## [2025.07.31.a9ed0a6] - 2025-07-31

### Changed
- chore(release): ship it 2025.07.31.5209809
- feat(testing): Add test suite for launch monitor
- wip


## [2025.07.31.5209809] - 2025-07-31

### Changed
- wip


## [2025.07.29.3afc6e0] - 2025-07-29

### Changed
- docs: Add task for Hold/Scrub notifications to TODO.md
- docs: Add TODO.md with Persons in Space task and other features


## [2025.07.29.3050a3e] - 2025-07-29

### Changed
- No new changes to report.


## [2025.07.23.41281e6] - 2025-07-23

### Changed
- fix: Correct schema type for debug in config.yaml to bool


## [2025.07.23.e0c8454] - 2025-07-23

### Changed
- fix: Add debug option to config and update middleware for debug environment
- fix: Ensure whatsapp.groupId and debug are correctly loaded from Home Assistant options


## [2025.07.23.b0f6e3b] - 2025-07-23

### Changed
- fix: Adjust test endpoint middleware and remove whatsapp_group_id from config.yaml


## [2025.07.23.7c20079] - 2025-07-23

### Changed
- fix: Update whatsapp.groupId to use user-defined options in config.yaml
- fix: Ensure whatsapp.groupId is correctly loaded from Home Assistant options
- fix: Map whatsapp_group_id from Home Assistant options to internal config


## [2025.07.23.0cc4bc8] - 2025-07-23

### Changed
- feat: add endpoint to retrieve WhatsApp Group ID and create config checker
- fix: Ensure correct WhatsApp Group ID is loaded by removing flattenedConfig


## [2025.07.23.66d4149] - 2025-07-23

### Changed
- fix: Correct YAML syntax in config.yaml for Home Assistant add-on


## [2025.07.23.93c04f5] - 2025-07-23

### Changed
- chore(release): ship it 2025.07.23.1
- feat: Add a new feature for prod changelog testing
- chore(release): ship it 2025.07.23.1


## [2025.07.23.1] - 2025-07-23

### Changed
- feat: Add a new feature for prod changelog testing


## [2025.07.23.1] - 2025-07-23

### Changed
- No new features to report since last merge with DEV.


## [2025.07.23.cf56a88] - 2025-07-23

### Changed
- fix: Add a dummy fix for changelog testing
- chore(release): ship it 2025.07.23.1
- chore(release): ship it


## [2025.07.23.1] - 2025-07-23

### Changed
- No new features to report since last merge with DEV.


## [] - 2025-07-23

### Changed
- No new features to report since last merge with DEV.


## [2025.07.23.5b130e5] - 2025-07-23

### Changed
- No new changes to report.


## [2025.07.23.d75a6c1] - 2025-07-23

### Changed
- No new changes to report.


## [2025.07.23.175a2da] - 2025-07-23

### Changed
- feat: add test endpoints for daily digest and launch notification in debug mode
- feat: add people in space to daily digest and fix test


## [2025.07.23.1] - 2025-07-23

### Changed
- chore(release): ship it 2025.07.23.c7d64df
- feat: add test endpoints for daily digest and launch notification in debug mode
- chore(release): ship it 2025.07.23.3d832e3
- feat: add people in space to daily digest and fix test
- fix(shipit): update shipit script to improve versioning and environment handling
- fix(shipit): improve changelog generation by using last 'ship it' commit as reference
- feat(shipit): update dashboard header based on environment
- chore(release): ship it 2025.07.22.891fc11
- fix(shipit): Make config.dev.yaml update conditional
- revert(dev): Revert DEV branch to main
- chore: Manually set version to 2025.07.14.5 on main branch


## [2025.07.23.c7d64df] - 2025-07-23

### Changed
- feat: add test endpoints for daily digest and launch notification in debug mode


## [2025.07.23.3d832e3] - 2025-07-23

### Changed
- feat: add people in space to daily digest and fix test
- fix(shipit): update shipit script to improve versioning and environment handling
- fix(shipit): improve changelog generation by using last 'ship it' commit as reference
- feat(shipit): update dashboard header based on environment


## [2025.07.22.891fc11] - 2025-07-22

### Changed
- No new changes to report.


## [0.0.81] - 2025-07-15

### Changed
- feat: Add number of people in space to daily digest
- feat: Conditionally display startup message based on debug mode
- chore: remove config.dev.yaml from main branch
- chore(release): ship it 0.0.80
- fix(shipit): correctly set addon name for prod
- chore(release): ship it 0.0.79
- fix(shipit): make config.yaml version update robust
- fix(version): correct addon version in config.yaml
- chore(release): ship it 0.0.78
- feat: improve logging and stabilize WhatsApp connection
- feat: Update sessionName in config.dev.yaml for better test isolation
- fix: Increase Puppeteer protocolTimeout to prevent timeouts
- feat: Implement DEV-specific configuration loading for testing
- chore(release): ship it 0.0.77
- feat: Enable CORS for Cloudflare Tunnel
- chore(release): ship it 0.0.76
- fix: Increase Puppeteer timeouts to prevent ProtocolError
- chore(release): ship it 0.0.76
- fix: Ensure Dockerfile version increments correctly
- feat: Automate meaningful CHANGELOG updates
- chore(release): ship it 0.0.75
- docs: Update GEMINI.md with specific WhatsApp Group ID
- fix: Update WhatsApp Group ID based on diagnostic
- chore(release): ship it 0.0.74
- docs: Add venom-bot troubleshooting to GEMINI.md
- chore(release): ship it 0.0.73
- fix: Default hardcoded config for production
- fix: Incomplete "ship it" process and checklist
- feat: Configure for production deployment
- chore(release): ship it 0.0.72
- chore(release): ship it 0.0.70
- chore(release): ship it ..1
- chore(release): ship it ..1
- refactor: Simplify run.sh startup and remove sleep
- feat: Add debug logging for config loading in config-loader.js
- fix: remove startup log message from run.sh
- chore: add js-yaml dependency and update run.sh startup message
- fix: Correct config.yaml fallback path in config-loader.js
- feat: Populate config.yaml with direct configuration values
- fix: Correct templates directory path
- fix: Remove duplicate YAML document in config.yaml
- refactor: Simplify config.yaml structure
- fix: Remove unbound variable check and bashio logging from run.sh
- fix: Provide valid default values in options section of config.yaml
- fix: Add empty options section to config.yaml
- fix: Quote environment variables in config.yaml
- feat: Move core config to environment section
- chore: bump version to 0.0.69 in Dockerfiles and config.yaml
- feat: Update GEMINI.md with comprehensive \"Ship It\" checklist
- feat: Ship it - Version 0.0.69 with comprehensive fixes and improvements
- fix: Enable Supervisor API access for add-on
- fix: Handle null/undefined env vars
- feat: Add production deployment checklist to GEMINI.md
- fix: Correct package.json path in dashboard.js
- feat: Make WhatsApp group name search configurable
- fix: Correct all import order dependencies in index.js
- fix: Import helpers module before use in index.js
- fix: Import messaging module before use in index.js
- feat: update WHATSAPP_GROUP_ID in config.yaml for development
- feat: update application name for development environment
- fix: Import api module before use in index.js
- feat: Update default WHATSAPP_GROUP_ID for dev branch
- fix: Import messageTemplates before use in index.js
- lowercase
- fix: Correctly import scheduleDailyDigest in index.js
- fix: Initialize logger before use in index.js
- fix: Remove *.sh from .dockerignore
- fix: Explicitly copy and execute run.sh in Dockerfile
- fix: Adjust Dockerfile for run.sh path and permissions
- refactor: Revert build structure to root and remove build_dir
- fix: Revert config.yaml to use build_dir for local build
- fix: Specify Dockerfile path directly in config.yaml
- fix: Update README.md version to 0.0.68
- feat: Relocate Dockerfile and run.sh to addon_build and update config.yaml
- fix: Ensure config.yaml version is 0.0.68
- fix: Update Dockerfile BUILD_VERSION to 0.0.68
- fix: Restore run.sh to root directory
- feat: Restore icon.png and logo.png to root
- feat: Add clear logs button and improve version logging
- refactor: Simplify add-on codebase structure
- feat: Synchronize nested add-on codebase with root
-  added a new log statement to index.js that will explicitly print the application version, the CI environment variable   status, and the process.stdout.isTTY status right at startup.
- remove logs functionaltiy
- added pjson for version...
- add version number to log file
- uped the version
- new logos
- feat: Improve logging and error handling
- feat: Improve logging and error handling
- fixed error in main space launch notifier
- fix(messaging): Remove duplicate connectToWhatsApp function
- Refactor: Clean up log output in space-launch-notifier/utils/messaging.js
- Refactor: Clean up log output and remove progress indicators
- Revert: Set WHATSAPP_GROUP_ID to production value in main branch
- Fix: Update default WhatsApp group ID in config-loader.js
- Update config.yaml version to 0.0.64 and set correct WHATSAPP_GROUP_ID in main branch
- Update config.yaml version to 0.0.64 in DEV branch
- Release: Version 0.0.64
- Release: Version 0.0.64
- Release: Version 0.0.62
- fix: update WhatsApp group ID to production ID in config files
- fix: change WhatsApp group ID in configuration files to production id
- Release: Version 0.0.61
- chore: update project documentation and configuration settings
- Configure DEV branch with development settings
- Configure main branch with production settings
- Configure DEV branch with development settings
- chore(release): ship it 0.0.60
- chore: add automated 'ship it' script for versioning and changelog updates
- chore(release): bump version to 0.0.59
- chore(release): bump version to 0.0.58
- fix(build): force clean npm install in Dockerfile
- chore(release): bump version to 0.0.57
- fix(deps): ensure moment-timezone is installed in nested folder
- chore(release): bump version to 0.0.56
- fix(build): use npm install to ensure dependencies are correctly installed
- fix(app): resolve startup issues and bump version to 0.0.55
- chore: bump version to 0.0.55 and add moment-timezone dependency
- chore: update run script to use dynamic versioning and enhance changelog
- chore: bump version to 0.0.54 and update cSpell words in settings
- chore: Add debug script for environment variable inspection and update test scripts
- chore: Update Dockerfile to enhance bashio installation process
- chore: Refactor Dockerfile to improve bashio installation process
- chore: Update Space Launch Notifier logo and icon to rocket emoji
- chore: Improve bashio installation process in Dockerfile
- chore: Update Space Launch Notifier to version 0.0.53 with enhanced version management and consistency checks
- chore: Define default build version in Dockerfile to 0.0.53
- chore: Update Dockerfile to set version to 0.0.53
- feat: Update Space Launch Notifier logo and icon to rocket emoji
- chore: Bump version to 0.0.53 with enhanced version management and consistency checks
- ``` chore: Improve bashio installation process in Dockerfile ```
- chore: Update Dockerfile dependencies and bashio installation process
- Fix bashio installation for v0.0.52
- ``` chore: Update Space Launch Notifier to version 0.0.52 with improved bashio integration and error handling ```
- Update to version 0.0.52: Enhance timezone configuration and documentation
- Update to version 0.0.51: Restore rocket-launch icon, update changelog, and add timezone configuration
- Fix Home Assistant add-on repository structure - Add missing repository.json and icons
- Fix Home Assistant add-on repository structure - Add missing repository.json and proper directory structure
- Fix: Convert to single add-on structure
- Update README to version 0.0.51
- Update to version 0.0.51: Restore rocket-launch icon and add timezone validation
- Update README and docs to version 0.0.50
- Fix npm install issues in Dockerfile - remove moment-timezone and use simpler npm install
- Fix Dockerfile base image reference for Home Assistant
- Fix Home Assistant build environment - remove invalid codenotary config
- Release version 0.0.50 - Cleanup nested directory structure
- Fix timezone handling in daily digest scheduler
- v0.0.48: Remove redundant nested directory structure and cleanup codebase


## [0.0.80] - 2025-07-14

### Changed
- fix(shipit): correctly set addon name for prod
- chore(release): ship it 0.0.79
- fix(shipit): make config.yaml version update robust
- fix(version): correct addon version in config.yaml
- chore(release): ship it 0.0.78
- feat: improve logging and stabilize WhatsApp connection
- feat: Update sessionName in config.dev.yaml for better test isolation
- fix: Increase Puppeteer protocolTimeout to prevent timeouts
- feat: Implement DEV-specific configuration loading for testing
- chore(release): ship it 0.0.77
- feat: Enable CORS for Cloudflare Tunnel
- chore(release): ship it 0.0.76
- fix: Increase Puppeteer timeouts to prevent ProtocolError
- chore(release): ship it 0.0.76
- fix: Ensure Dockerfile version increments correctly
- feat: Automate meaningful CHANGELOG updates
- chore(release): ship it 0.0.75
- docs: Update GEMINI.md with specific WhatsApp Group ID
- fix: Update WhatsApp Group ID based on diagnostic
- chore(release): ship it 0.0.74
- docs: Add venom-bot troubleshooting to GEMINI.md
- chore(release): ship it 0.0.73
- fix: Default hardcoded config for production
- fix: Incomplete "ship it" process and checklist
- feat: Configure for production deployment
- chore(release): ship it 0.0.72
- chore(release): ship it 0.0.70
- chore(release): ship it ..1
- chore(release): ship it ..1
- refactor: Simplify run.sh startup and remove sleep
- feat: Add debug logging for config loading in config-loader.js
- fix: remove startup log message from run.sh
- chore: add js-yaml dependency and update run.sh startup message
- fix: Correct config.yaml fallback path in config-loader.js
- feat: Populate config.yaml with direct configuration values
- fix: Correct templates directory path
- fix: Remove duplicate YAML document in config.yaml
- refactor: Simplify config.yaml structure
- fix: Remove unbound variable check and bashio logging from run.sh
- fix: Provide valid default values in options section of config.yaml
- fix: Add empty options section to config.yaml
- fix: Quote environment variables in config.yaml
- feat: Move core config to environment section
- chore: bump version to 0.0.69 in Dockerfiles and config.yaml
- feat: Update GEMINI.md with comprehensive \"Ship It\" checklist
- feat: Ship it - Version 0.0.69 with comprehensive fixes and improvements
- fix: Enable Supervisor API access for add-on
- fix: Handle null/undefined env vars
- feat: Add production deployment checklist to GEMINI.md
- fix: Correct package.json path in dashboard.js
- feat: Make WhatsApp group name search configurable
- fix: Correct all import order dependencies in index.js
- fix: Import helpers module before use in index.js
- fix: Import messaging module before use in index.js
- feat: update WHATSAPP_GROUP_ID in config.yaml for development
- feat: update application name for development environment
- fix: Import api module before use in index.js
- feat: Update default WHATSAPP_GROUP_ID for dev branch
- fix: Import messageTemplates before use in index.js
- lowercase
- fix: Correctly import scheduleDailyDigest in index.js
- fix: Initialize logger before use in index.js
- fix: Remove *.sh from .dockerignore
- fix: Explicitly copy and execute run.sh in Dockerfile
- fix: Adjust Dockerfile for run.sh path and permissions
- refactor: Revert build structure to root and remove build_dir
- fix: Revert config.yaml to use build_dir for local build
- fix: Specify Dockerfile path directly in config.yaml
- fix: Update README.md version to 0.0.68
- feat: Relocate Dockerfile and run.sh to addon_build and update config.yaml
- fix: Ensure config.yaml version is 0.0.68
- fix: Update Dockerfile BUILD_VERSION to 0.0.68
- fix: Restore run.sh to root directory
- feat: Restore icon.png and logo.png to root
- feat: Add clear logs button and improve version logging
- refactor: Simplify add-on codebase structure
- feat: Synchronize nested add-on codebase with root
-  added a new log statement to index.js that will explicitly print the application version, the CI environment variable   status, and the process.stdout.isTTY status right at startup.
- remove logs functionaltiy
- added pjson for version...
- add version number to log file
- uped the version
- new logos
- feat: Improve logging and error handling
- feat: Improve logging and error handling
- fixed error in main space launch notifier
- fix(messaging): Remove duplicate connectToWhatsApp function
- Refactor: Clean up log output in space-launch-notifier/utils/messaging.js
- Refactor: Clean up log output and remove progress indicators
- Revert: Set WHATSAPP_GROUP_ID to production value in main branch
- Fix: Update default WhatsApp group ID in config-loader.js
- Update config.yaml version to 0.0.64 and set correct WHATSAPP_GROUP_ID in main branch
- Update config.yaml version to 0.0.64 in DEV branch
- Release: Version 0.0.64
- Release: Version 0.0.64
- Release: Version 0.0.62
- fix: update WhatsApp group ID to production ID in config files
- fix: change WhatsApp group ID in configuration files to production id
- Release: Version 0.0.61
- chore: update project documentation and configuration settings
- Configure DEV branch with development settings
- Configure main branch with production settings
- Configure DEV branch with development settings
- chore(release): ship it 0.0.60
- chore: add automated 'ship it' script for versioning and changelog updates
- chore(release): bump version to 0.0.59
- chore(release): bump version to 0.0.58
- fix(build): force clean npm install in Dockerfile
- chore(release): bump version to 0.0.57
- fix(deps): ensure moment-timezone is installed in nested folder
- chore(release): bump version to 0.0.56
- fix(build): use npm install to ensure dependencies are correctly installed
- fix(app): resolve startup issues and bump version to 0.0.55
- chore: bump version to 0.0.55 and add moment-timezone dependency
- chore: update run script to use dynamic versioning and enhance changelog
- chore: bump version to 0.0.54 and update cSpell words in settings
- chore: Add debug script for environment variable inspection and update test scripts
- chore: Update Dockerfile to enhance bashio installation process
- chore: Refactor Dockerfile to improve bashio installation process
- chore: Update Space Launch Notifier logo and icon to rocket emoji
- chore: Improve bashio installation process in Dockerfile
- chore: Update Space Launch Notifier to version 0.0.53 with enhanced version management and consistency checks
- chore: Define default build version in Dockerfile to 0.0.53
- chore: Update Dockerfile to set version to 0.0.53
- feat: Update Space Launch Notifier logo and icon to rocket emoji
- chore: Bump version to 0.0.53 with enhanced version management and consistency checks
- ``` chore: Improve bashio installation process in Dockerfile ```
- chore: Update Dockerfile dependencies and bashio installation process
- Fix bashio installation for v0.0.52
- ``` chore: Update Space Launch Notifier to version 0.0.52 with improved bashio integration and error handling ```
- Update to version 0.0.52: Enhance timezone configuration and documentation
- Update to version 0.0.51: Restore rocket-launch icon, update changelog, and add timezone configuration
- Fix Home Assistant add-on repository structure - Add missing repository.json and icons
- Fix Home Assistant add-on repository structure - Add missing repository.json and proper directory structure
- Fix: Convert to single add-on structure
- Update README to version 0.0.51
- Update to version 0.0.51: Restore rocket-launch icon and add timezone validation
- Update README and docs to version 0.0.50
- Fix npm install issues in Dockerfile - remove moment-timezone and use simpler npm install
- Fix Dockerfile base image reference for Home Assistant
- Fix Home Assistant build environment - remove invalid codenotary config
- Release version 0.0.50 - Cleanup nested directory structure
- Fix timezone handling in daily digest scheduler
- v0.0.48: Remove redundant nested directory structure and cleanup codebase


## [0.0.79] - 2025-07-14

### Changed
- fix(shipit): make config.yaml version update robust
- fix(version): correct addon version in config.yaml
- chore(release): ship it 0.0.78
- feat: improve logging and stabilize WhatsApp connection
- feat: Update sessionName in config.dev.yaml for better test isolation
- fix: Increase Puppeteer protocolTimeout to prevent timeouts
- feat: Implement DEV-specific configuration loading for testing
- chore(release): ship it 0.0.77
- feat: Enable CORS for Cloudflare Tunnel
- chore(release): ship it 0.0.76
- fix: Increase Puppeteer timeouts to prevent ProtocolError
- chore(release): ship it 0.0.76
- fix: Ensure Dockerfile version increments correctly
- feat: Automate meaningful CHANGELOG updates
- chore(release): ship it 0.0.75
- docs: Update GEMINI.md with specific WhatsApp Group ID
- fix: Update WhatsApp Group ID based on diagnostic
- chore(release): ship it 0.0.74
- docs: Add venom-bot troubleshooting to GEMINI.md
- chore(release): ship it 0.0.73
- fix: Default hardcoded config for production
- fix: Incomplete "ship it" process and checklist
- feat: Configure for production deployment
- chore(release): ship it 0.0.72
- chore(release): ship it 0.0.70
- chore(release): ship it ..1
- chore(release): ship it ..1
- refactor: Simplify run.sh startup and remove sleep
- feat: Add debug logging for config loading in config-loader.js
- fix: remove startup log message from run.sh
- chore: add js-yaml dependency and update run.sh startup message
- fix: Correct config.yaml fallback path in config-loader.js
- feat: Populate config.yaml with direct configuration values
- fix: Correct templates directory path
- fix: Remove duplicate YAML document in config.yaml
- refactor: Simplify config.yaml structure
- fix: Remove unbound variable check and bashio logging from run.sh
- fix: Provide valid default values in options section of config.yaml
- fix: Add empty options section to config.yaml
- fix: Quote environment variables in config.yaml
- feat: Move core config to environment section
- chore: bump version to 0.0.69 in Dockerfiles and config.yaml
- feat: Update GEMINI.md with comprehensive \"Ship It\" checklist
- feat: Ship it - Version 0.0.69 with comprehensive fixes and improvements
- fix: Enable Supervisor API access for add-on
- fix: Handle null/undefined env vars
- feat: Add production deployment checklist to GEMINI.md
- fix: Correct package.json path in dashboard.js
- feat: Make WhatsApp group name search configurable
- fix: Correct all import order dependencies in index.js
- fix: Import helpers module before use in index.js
- fix: Import messaging module before use in index.js
- feat: update WHATSAPP_GROUP_ID in config.yaml for development
- feat: update application name for development environment
- fix: Import api module before use in index.js
- feat: Update default WHATSAPP_GROUP_ID for dev branch
- fix: Import messageTemplates before use in index.js
- lowercase
- fix: Correctly import scheduleDailyDigest in index.js
- fix: Initialize logger before use in index.js
- fix: Remove *.sh from .dockerignore
- fix: Explicitly copy and execute run.sh in Dockerfile
- fix: Adjust Dockerfile for run.sh path and permissions
- refactor: Revert build structure to root and remove build_dir
- fix: Revert config.yaml to use build_dir for local build
- fix: Specify Dockerfile path directly in config.yaml
- fix: Update README.md version to 0.0.68
- feat: Relocate Dockerfile and run.sh to addon_build and update config.yaml
- fix: Ensure config.yaml version is 0.0.68
- fix: Update Dockerfile BUILD_VERSION to 0.0.68
- fix: Restore run.sh to root directory
- feat: Restore icon.png and logo.png to root
- feat: Add clear logs button and improve version logging
- refactor: Simplify add-on codebase structure
- feat: Synchronize nested add-on codebase with root
-  added a new log statement to index.js that will explicitly print the application version, the CI environment variable   status, and the process.stdout.isTTY status right at startup.
- remove logs functionaltiy
- added pjson for version...
- add version number to log file
- uped the version
- new logos
- feat: Improve logging and error handling
- feat: Improve logging and error handling
- fixed error in main space launch notifier
- fix(messaging): Remove duplicate connectToWhatsApp function
- Refactor: Clean up log output in space-launch-notifier/utils/messaging.js
- Refactor: Clean up log output and remove progress indicators
- Revert: Set WHATSAPP_GROUP_ID to production value in main branch
- Fix: Update default WhatsApp group ID in config-loader.js
- Update config.yaml version to 0.0.64 and set correct WHATSAPP_GROUP_ID in main branch
- Update config.yaml version to 0.0.64 in DEV branch
- Release: Version 0.0.64
- Release: Version 0.0.64
- Release: Version 0.0.62
- fix: update WhatsApp group ID to production ID in config files
- fix: change WhatsApp group ID in configuration files to production id
- Release: Version 0.0.61
- chore: update project documentation and configuration settings
- Configure DEV branch with development settings
- Configure main branch with production settings
- Configure DEV branch with development settings
- chore(release): ship it 0.0.60
- chore: add automated 'ship it' script for versioning and changelog updates
- chore(release): bump version to 0.0.59
- chore(release): bump version to 0.0.58
- fix(build): force clean npm install in Dockerfile
- chore(release): bump version to 0.0.57
- fix(deps): ensure moment-timezone is installed in nested folder
- chore(release): bump version to 0.0.56
- fix(build): use npm install to ensure dependencies are correctly installed
- fix(app): resolve startup issues and bump version to 0.0.55
- chore: bump version to 0.0.55 and add moment-timezone dependency
- chore: update run script to use dynamic versioning and enhance changelog
- chore: bump version to 0.0.54 and update cSpell words in settings
- chore: Add debug script for environment variable inspection and update test scripts
- chore: Update Dockerfile to enhance bashio installation process
- chore: Refactor Dockerfile to improve bashio installation process
- chore: Update Space Launch Notifier logo and icon to rocket emoji
- chore: Improve bashio installation process in Dockerfile
- chore: Update Space Launch Notifier to version 0.0.53 with enhanced version management and consistency checks
- chore: Define default build version in Dockerfile to 0.0.53
- chore: Update Dockerfile to set version to 0.0.53
- feat: Update Space Launch Notifier logo and icon to rocket emoji
- chore: Bump version to 0.0.53 with enhanced version management and consistency checks
- ``` chore: Improve bashio installation process in Dockerfile ```
- chore: Update Dockerfile dependencies and bashio installation process
- Fix bashio installation for v0.0.52
- ``` chore: Update Space Launch Notifier to version 0.0.52 with improved bashio integration and error handling ```
- Update to version 0.0.52: Enhance timezone configuration and documentation
- Update to version 0.0.51: Restore rocket-launch icon, update changelog, and add timezone configuration
- Fix Home Assistant add-on repository structure - Add missing repository.json and icons
- Fix Home Assistant add-on repository structure - Add missing repository.json and proper directory structure
- Fix: Convert to single add-on structure
- Update README to version 0.0.51
- Update to version 0.0.51: Restore rocket-launch icon and add timezone validation
- Update README and docs to version 0.0.50
- Fix npm install issues in Dockerfile - remove moment-timezone and use simpler npm install
- Fix Dockerfile base image reference for Home Assistant
- Fix Home Assistant build environment - remove invalid codenotary config
- Release version 0.0.50 - Cleanup nested directory structure
- Fix timezone handling in daily digest scheduler
- v0.0.48: Remove redundant nested directory structure and cleanup codebase


## [0.0.78] - 2025-07-13

### Changed
- feat: improve logging and stabilize WhatsApp connection
- feat: Update sessionName in config.dev.yaml for better test isolation
- fix: Increase Puppeteer protocolTimeout to prevent timeouts
- feat: Implement DEV-specific configuration loading for testing
- feat: Automate meaningful CHANGELOG updates
- docs: Update GEMINI.md with specific WhatsApp Group ID
- docs: Add venom-bot troubleshooting to GEMINI.md
- feat: Configure for production deployment
- refactor: Simplify run.sh startup and remove sleep
- feat: Add debug logging for config loading in config-loader.js
- feat: Populate config.yaml with direct configuration values
- refactor: Simplify config.yaml structure
- feat: Move core config to environment section
- feat: Update GEMINI.md with comprehensive "Ship It" checklist
- feat: Add production deployment checklist to GEMINI.md
- feat: Make WhatsApp group name search configurable
- feat: update WHATSAPP_GROUP_ID in config.yaml for development
- feat: update application name for development environment
- feat: Update default WHATSAPP_GROUP_ID for dev branch lowercase
- refactor: Revert build structure to root and remove build_dir
- feat: Relocate Dockerfile and run.sh to addon_build and update config.yaml
- feat: Restore icon.png and logo.png to root
- feat: Add clear logs button and improve version logging
- refactor: Simplify add-on codebase structure
- feat: Synchronize nested add-on codebase with root
-  added a new log statement to index.js that will explicitly print the application version, the CI environment variable   status, and the process.stdout.isTTY status right at startup.
- remove logs functionality
- added pjson for version...
- add version number to log file
- uped the version
- new logos
- feat: Improve logging and error handling
- feat: Improve logging and error handling
- Refactor: Clean up log output in space-launch-notifier/utils/messaging.js
- Refactor: Clean up log output and remove progress indicators
- Revert: Set WHATSAPP_GROUP_ID to production value in main branch
- Fix: Update default WhatsApp Group ID in config-loader.js
- Update config.yaml version to 0.0.64 and set correct WHATSAPP_GROUP_ID in main branch
- Update config.yaml version to 0.0.64 in DEV branch
- Release: Version 0.0.64
- Release: Version 0.0.64
- Release: Version 0.0.62
- Release: Version 0.0.61
- Configure DEV branch with development settings
- Configure main branch with production settings
- Configure DEV branch with development settings
- feat: Update Space Launch Notifier logo and icon to rocket emoji
- Fix bashio installation for v0.0.52
- Update to version 0.0.52: Enhance timezone configuration and documentation
- Update to version 0.0.51: Restore rocket-launch icon, update changelog, and add timezone configuration
- Fix Home Assistant add-on repository structure - Add missing repository.json and icons
- Fix Home Assistant add-on repository structure - Add missing repository.json and proper directory structure
- Fix: Convert to single add-on structure
- Update README to version 0.0.51
- Update to version 0.0.51: Restore rocket-launch icon and add timezone validation
- Update README and docs to version 0.0.50
- Fix npm install issues in Dockerfile - remove moment-timezone and use simpler npm install
- Fix Dockerfile base image reference for Home Assistant
- Fix Home Assistant build environment - remove invalid codenotary config
- Release version 0.0.50 - Cleanup nested directory structure
- Fix timezone handling in daily digest scheduler
- v0.0.48: Remove redundant nested directory structure and cleanup codebase


## [0.0.77] - 2025-07-10

### Changed
- feat: Enable CORS for Cloudflare Tunnel

## [0.0.76] - 2025-07-09

### Changed
- Aligned `dev` branch with `main` branch, including dependencies, documentation, and scripts.
- Set `dev` branch to version `0.0.76`.
- Configured `dev` branch for development environment (port 3033, dev API URLs, etc.).

## [0.0.75] - 2025-07-09

### Changed
- Version bumped to 0.0.75.

## [0.0.74] - 2025-07-09

### Changed
- Version bumped to 0.0.74.

## [0.0.73] - 2025-07-09

### Changed
- Version bumped to 0.0.73.

## [0.0.72] - 2025-07-09

### Changed
- Configured for production deployment.
- Updated WhatsApp Group ID based on diagnostic.
- Added venom-bot troubleshooting to GEMINI.md.
- Fixed incomplete "ship it" process and checklist.
- Default hardcoded config for production updated.

## [0.0.70] - 2025-07-09

### Changed
- Version bumped to 0.0.70.

## [..1] - 2025-07-09

### Changed
- Version bumped to ..1.

## [..1] - 2025-07-09

### Changed
- Version bumped to ..1.

# Changelog

All notable changes to the Space Launch Notifier will be documented in this file.

## [0.0.69] - 2025-07-08

### Fixed
- Resolved all `ReferenceError` issues in `index.js` by ensuring correct import order for `api`, `messaging`, `helpers`, `connectionMonitor`, `urlTracker`, `moment`, and `node-schedule`.
- Implemented robust handling for `null` and `undefined` string values passed as environment variables from Home Assistant Supervisor in `utils/config-loader.js`, ensuring default configuration values are correctly applied.
- Corrected `package.json` path in `dashboard.js`.
- Updated `Dockerfile` to explicitly copy and execute `run.sh` at the image root, resolving persistent `run.sh` not found errors during Docker build.
- Removed `*.sh` from `.dockerignore` to ensure `run.sh` is included in the build context.
- Updated `BUILD_VERSION` in `Dockerfile` and `version` in `config.yaml` to `0.0.68`.
- Added explicit logging for raw `bashio::config` values in `run.sh` for improved debugging of configuration loading.
- Updated fallback group name search in `utils/messaging.js` to use configurable `WHATSAPP_GROUP_NAME_SEARCH` and log the found group ID.

### Added
- Added 'Clear Logs' button to the dashboard for easier log management.
- Implemented explicit logging of application version, CI environment variable, and TTY status at startup for improved debugging.
- Added `WHATSAPP_GROUP_NAME_SEARCH` configuration option.

### Changed
- Simplified codebase structure by removing the nested add-on directory and consolidating essential files into the root.
- Synchronized `package.json` versions between root and add-on build context.
- Updated `run.sh` to set the `CI` environment variable for non-interactive logging.
- Updated `README.md` version to `0.0.68`.

## [0.0.68] - 2025-07-07

### Added
- Added 'Clear Logs' button to the dashboard for easier log management.
- Implemented explicit logging of application version, CI environment variable, and TTY status at startup for improved debugging.

### Changed
- Simplified codebase structure by removing the nested add-on directory and consolidating essential files into the root.
- Synchronized `package.json` versions between root and add-on build context.
- Updated Dockerfile to reflect the new, streamlined directory structure.

## [0.0.67] - 2025-07-07

### Fixed
- Disabled interactive progress indicators in non-TTY environments to prevent `UnicodeDecodeError` in Home Assistant logs.
- Added a global error handler to catch unhandled promise rejections and provide better error logging.
- Implemented more robust error handling in `utils/messaging.js` to prevent crashes from WhatsApp server errors.

### Changed
- Updated `run.sh` to set the `CI` environment variable for non-interactive logging.

## [0.0.66] - 2025-07-07

### Fixed
- Disabled interactive progress indicators in non-TTY environments to prevent `UnicodeDecodeError` in Home Assistant logs.
- Added a global error handler to catch unhandled promise rejections and provide better error logging.

### Added
- Placeholder `icon.png` and `logo.png` files for the Home Assistant add-on.

## [0.0.65] - 2025-07-07

### Fixed
- Removed duplicated `connectToWhatsApp` function in `utils/messaging.js`.

# Changelog

## [0.0.64] - 2025-07-04

### Changed
- Updated version to `0.0.64`.

## [0.0.63] - 2025-07-04

### Changed
- Updated version to `0.0.63`.

## [0.0.62] - 2025-07-04

### Changed
- Updated version to `0.0.62`.

## [0.0.60] - 2025-07-02

### Changed
- Removed Config and Language sections from dashboard.
- Added dynamic version display to dashboard.

## [0.0.59] - 2025-07-01

### Changed
- Updated version to `0.0.59`.
- Improved log streaming robustness in dashboard.

## [0.0.58] - 2025-07-01

### Changed
- Updated version to `0.0.58`.

## [0.0.57] - 2025-07-01

### Changed
- Updated version to `0.0.57`.

## [0.0.55] - 2025-07-01

### Fixed
- Corrected an issue where the `run.sh` script would fail due to the `bashio` library not being sourced correctly.
- Added the missing `moment-timezone` dependency to `package.json` to prevent crashes on startup.

## [0.0.54] - 2025-06-28

### Changed
- Updated version to `0.0.54`.
- Improved error handling for missing `WHATSAPP_GROUP_ID`.
- Fixed issues with `bashio::config` commands in `run.sh`.

-
- ## [0.0.53] - 2025-05-29

### Added
- Enhanced version management process
- Improved consistency checks across application files
- Streamlined version update workflow

## [0.0.52] - 2025-05-28

### Added
- Enhanced version management across application files
- Improved version consistency checks
- Streamlined version update process

## [0.0.51] - 2025-05-27

### Fixed
- Minor code cleanup and optimization
- Improved error handling in utility modules

## [0.0.50] - 2025-05-26

### Added
- Additional performance improvements
- Enhanced logging capabilities

## [0.0.48] - 2025-05-23

### Fixed
- Removed redundant nested directory structure in utils folder
- Improved codebase organization and reduced duplication
- Added cleanup script for nested directories

## [0.0.47] - 2023-05-15

### Added
- System resource monitoring and performance improvements
- Enhanced error handling for API connections

## [0.0.46] - 2023-05-14

### Added
- API Rate Limit Status

## [0.0.43] - 2023-05-13

### Fixed
- Fixed Docker build context issues
- Improved add-on directory structure
- Ensured all necessary files are included in the build context

## [0.0.42] - 2023-05-13

### Added
- Home Assistant add-on configuration
- Dockerfile for containerized deployment
- Multi-architecture build support via build.yaml
- Entry point script (run.sh) for the container
- GitHub token setup documentation
- Test script for add-on configuration validation

### Changed
- Updated version numbering to follow 0.0.x format
- Improved README with add-on installation instructions
- Enhanced environment detection for Home Assistant

### Fixed
- Proper path handling for Home Assistant environment
- Log directory configuration for add-on deployment