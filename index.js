// index.js - Main application file for Space Launch Notifier

// New structure: Export a function that takes config and state
module.exports = ({ config, state }) => {
    // Import functions from modules (ordered by dependency)
    const moment = require('moment');
    const schedule = require('node-schedule');
    const helpers = require('./utils/helpers');
    const languageManager = require('./utils/language-manager');
    const defaultConfig = require('./utils/default-config');
    const i18nLoader = require('./utils/i18n/loader');
    const messageTemplates = require('./utils/message-templates')(config, { moment, hasLivestream: helpers.hasLivestream, getLivestreamUrls: helpers.getLivestreamUrls, languageManager, defaultConfig, i18nLoader });

    const logger = require('./utils/logger');
    logger.initializeLogger(config);
    const log = logger.getLogger('main');

    // Initialize modules that depend on config/state
    const messaging = require('./utils/messaging')(config, state);
    const { connectToWhatsApp, closeWhatsAppConnection } = messaging;
    const connectionMonitor = require('./utils/connection-monitor')({ config, state, logger, messagingModule: messaging });
    const urlTracker = require('./utils/url-tracker');
    // Initialize state.launchesCache before it's used by the API module
    state.launchesCache = { data: [], timestamp: null };
    log.debug('Config before API module init:', config);
    log.debug('State before API module init:', state);
    log.debug('Config before API module init:', config);
    log.debug('State before API module init:', state);
    const api = require('./utils/api')(config, state);
    log.debug('API object after initialization:', api);
    log.debug('API object after initialization:', api);
    log.debug('API object after initialization:', api);
    const { startLaunchMonitor } = require('./launch-monitor')(config, state);
    const progress = require('./utils/progress-indicator');
    const dashboard = require('./dashboard')(config, state);

    // Add dashboard to state so other modules can access it
    state.dashboard = dashboard;

    log.debug('messageTemplates before daily-digest init:', messageTemplates);
    const { scheduleDailyDigest } = require('./daily-digest')(config, state, { configModule: { config, state }, messageTemplates, logger, api, messaging, helpers, connectionMonitor, urlTracker, moment, schedule, spacePeople: require('./utils/space-people') });
    const templateWatcher = require('./utils/template-watcher')(config, { fs: require('fs'), path: require('path'), messageTemplates });

    // Ensure 'api' is fully initialized before 'updateHandler' is required
    log.debug('API object before destructuring:', api);
    const { getLatestLaunchData } = api;
    const updateHandler = require('./utils/update-handler')(config, state, { schedule, moment, getLatestLaunchData, getLivestreamUrls: helpers.getLivestreamUrls, sendGroupMessage: messaging.sendGroupMessage, messageTemplates, logger });

    // Add a global error handler for unhandled promise rejections
    process.on('unhandledRejection', (reason, promise) => {
        log.error('Unhandled Rejection at:', promise, 'reason:', reason);
        log.error('--- SHUTDOWN ---');
        process.exit(1);
    });

    // Explicitly log version and environment details for debugging Home Assistant issues
    log.info(`App Version: ${config.version}, CI: ${process.env.CI}, isTTY: ${process.stdout.isTTY}`);

    /**
     * Graceful shutdown function with enhanced cleanup
     */
    function gracefulShutdown() {
        log.info('Shutting down Space Launch Notifier...');

        // Create a spinner for the shutdown process
        const shutdownSpinner = progress.createSpinner(
            'Shutting down gracefully...',
            'COUNTDOWN',
            'yellow'
        );

        // Create a progress bar for the shutdown steps
        const shutdownProgress = progress.createProgressBar(
            3, // Total steps
            'Shutting down application',
            'yellow'
        );

        // Step 1: Cancel scheduled jobs
        shutdownProgress.update(1, 'Canceling scheduled jobs...');
        log.debug('Canceling scheduled jobs', { jobCount: state.scheduledJobs.length });
        state.scheduledJobs.forEach(job => {
            if (job) job.cancel();
        });

        // Step 2: Clean up lock files
        shutdownProgress.update(2, 'Cleaning up lock files...');
        try {
            const fs = require('fs');
            const path = require('path');
            const tokenDir = path.join(__dirname, config.paths.tokensDir, config.whatsapp.sessionName);

            const lockFiles = ['SingletonLock', 'SingletonCookie', 'SingletonSocket', 'DevToolsActivePort'];
            let removedCount = 0;
            lockFiles.forEach(file => {
                const filePath = path.join(tokenDir, file);
                if (fs.existsSync(filePath)) {
                    fs.unlinkSync(filePath);
                    removedCount++;
                }
            });
            shutdownSpinner.update(`Removed ${removedCount} lock files`);
        } catch (error) {
            shutdownSpinner.update(`Error cleaning up lock files: ${error.message}`);
        }

        // Step 3: Close WhatsApp connection
        shutdownProgress.update(3, 'Closing WhatsApp connection...');

        // Create a countdown for the force exit timeout
        const forceExitSeconds = Math.floor(config.whatsapp.connectionRetry.initWait / 1000);
        const forceExitTimer = progress.createCountdown(
            forceExitSeconds,
            'Force exit in:',
            () => {
                shutdownSpinner.stop('Forcing exit due to timeout', '⚠');
                process.exit(1);
            }
        );

        // Close WhatsApp connection
        closeWhatsAppConnection().finally(() => {
            // Cancel the force exit timer
            forceExitTimer.stop();

            // Complete the progress bar
            shutdownProgress.complete('Shutdown complete');

            // Final message
            shutdownSpinner.stop('Goodbye! Space Launch Notifier has been shut down.', '✓');
            log.info('Application shutdown complete');

            // Allow time for logs to be flushed
            setTimeout(() => {
                process.exit(0);
            }, 500);
        });
    }

    /**
     * Main function to start the application.
     * This function is exported to allow for easier testing.
     */
    async function startApplication() {
        log.info('Starting Space Launch WhatsApp Notifier...');

        try {
            // Step 1: Connect to WhatsApp
            log.info('Connecting to WhatsApp...');
            await connectToWhatsApp();

            // Step 2: Wait for initialization
            log.info(`Waiting ${config.whatsapp.connectionRetry.initWait / 1000} seconds for WhatsApp to stabilize...`);
            await new Promise(resolve => setTimeout(resolve, config.whatsapp.connectionRetry.initWait));
            log.info('WhatsApp initialization complete!');

            // Step 3: Start connection monitor
            log.info('Starting WhatsApp connection monitor...');
            connectionMonitor.startConnectionMonitor();

            // Step 4: Start template watcher
            log.info('Starting template watcher...');
            templateWatcher.startWatching();

            // Step 5: Start launch monitor
            log.info('Starting launch monitor...');
            startLaunchMonitor();

            // Step 6: Initialize daily digest
            log.info('Initializing daily digest...');
            await scheduleDailyDigest();

            // Step 7: Start dashboard
            log.info('Starting health dashboard...');
            let dashboardResult;
            try {
                dashboardResult = await dashboard.startDashboard();
                log.info(`Health dashboard started at http://localhost:${dashboardResult.port}`);
            } catch (error) {
                log.error('Failed to start dashboard', { error: error.message, stack: error.stack });
                log.warn('Health dashboard failed to start, continuing without it');
            }

            // Handle graceful shutdown
            process.on('SIGINT', gracefulShutdown);
            process.on('SIGTERM', gracefulShutdown);

            log.info('All systems initialized!');
            log.info('Space Launch Notifier is now running! 🚀');
            log.info('App started successfully!');
            log.info('Press Ctrl+C to stop the application');

            try {
                const port = dashboardResult ? dashboardResult.port : 3030;
                log.info(`Health dashboard available at: http://localhost:${port}`);
            } catch (error) {
                log.warn('Health dashboard is not available');
            }

            // Log system information
            log.info('System information', {
                environment: logger.isHomeAssistant() ? 'Home Assistant' : 'Standalone',
                nodeVersion: process.version,
                config: {
                    debug: config.debug,
                    whatsapp: {
                        sessionName: config.whatsapp.sessionName
                    },
                    dailyDigest: {
                        enabled: config.dailyDigest.enabled,
                        time: config.dailyDigest.time
                    }
                }
            });
        } catch (error) {
            log.error(`Fatal error: ${error.message}`, { error: error.message, stack: error.stack });
            process.exit(1);
        }
    }

    return {
        startApplication,
        gracefulShutdown // Export for testing purposes
    };
};

// If not in a test environment, start the application
if (process.env.NODE_ENV !== 'test') {
    const configLoader = require('./utils/config-loader');
    const loadedConfig = configLoader.loadConfig();
    const app = module.exports({ config: loadedConfig, state: {} });
    app.startApplication();
}
