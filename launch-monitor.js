// launch-monitor.js - Launch monitoring with enhanced webcast detection and image support

const moment = require('moment');
const schedule = require('node-schedule');
// Import config module without destructuring to avoid redeclaration
module.exports = (config, state) => {

// Import logger
const logger = require('./utils/logger');
const log = logger.getLogger('launch-monitor');

const { fetchUpcomingLaunches, getLatestLaunchData, getApiRateLimitStatus } = require('./utils/api');
const { sendGroupMessage, sendLaunchNotificationWithImage, sendLaunchDelayNotification } = require('./utils/messaging');
const { hasLivestream, getLivestreamUrls, checkLaunchDelay } = require('./utils/helpers');
const imageProvider = require('./utils/image-provider');
const updateHandler = require('./utils/update-handler');

/**
 * Determines if a launch is happening within the notification window and has a livestream
 * @param {Object} launch - Launch object from API
 * @returns {Boolean} Whether the launch should trigger a notification
 */
function isUpcomingLaunchWithStream(launch, now) {
    // If no launch time or no livestream, return false
    if (!launch.net || !hasLivestream(launch)) {
        return false;
    }

    const launchTime = moment(launch.net);

    // Time difference in seconds
    const secondsUntilLaunch = launchTime.diff(now, 'seconds', true);

    // Notification window in seconds
    const notificationWindowStart = config.notifications.defaultTime * 60;
    const notificationWindowEnd = (config.notifications.defaultTime - 1) * 60;

    // Check if launch is within the precise window and not already notified
    return (
        secondsUntilLaunch <= notificationWindowStart &&
        secondsUntilLaunch > notificationWindowEnd &&
        !state.notifiedLaunches.has(launch.id)
    );
}

/**
 * Start monitoring launches
 * @returns {Object} Scheduled job
 */
function startLaunchMonitor() {
    log.info('Starting launch monitor...');

    // Run once at startup to initialize cache
    fetchUpcomingLaunches().then(launches => {
        log.info(`Found ${launches.length} upcoming launches`);

        // Log the first launch to see what image fields are available
        if (launches.length > 0 && config.debug) {
            log.debug("Examining first launch for available image fields");
            imageProvider.logLaunchImageFields(launches[0]);
        }
    }).catch(err => {
        log.error('Initial fetch error', { error: err.message, stack: err.stack });
    });

    // Schedule check based on configured interval
    const cronExpression = `*/${config.notifications.checkInterval} * * * *`;
    const job = schedule.scheduleJob(cronExpression, async () => {
        try {
            // Log API rate limit status
            const rateLimitStatus = getApiRateLimitStatus();
            log.info(`Checking for upcoming launches (${moment().format('YYYY-MM-DD HH:mm:ss')})...`, {
                apiCalls: `${rateLimitStatus.callsInLastHour}/${rateLimitStatus.maxCalls}`,
                remaining: rateLimitStatus.remainingCalls,
                rateLimitReached: rateLimitStatus.rateLimitReached
            });

            const now = moment();
            let launches = await fetchUpcomingLaunches();

            // Check for imminent launches that need refresh
            for (let i = 0; i < launches.length; i++) {
                const launch = launches[i];
                const launchTime = moment(launch.net);
                const minutesUntilLaunch = launchTime.diff(now, 'minutes');

                // If launch is within 10 minutes, consider refreshing data
                if (minutesUntilLaunch >= 0 && minutesUntilLaunch <= 10) {
                    // Only do a direct lookup if:
                    // 1. We haven't checked it recently (within 5 minutes)
                    // 2. We still have API calls available (respecting rate limit)
                    const lastCheckTime = state.imminentLaunches.get(launch.id);
                    const timeSinceLastCheck = lastCheckTime ?
                        moment().diff(lastCheckTime, 'minutes') : 999;
                    const timeSinceLastApiCall = state.lastApiCallTime ?
                        moment().diff(state.lastApiCallTime, 'minutes') : 999;

                    log.info(`Launch ${launch.name} is imminent (T-${minutesUntilLaunch}m)`, {
                        launchId: launch.id,
                        launchTime: launchTime.format()
                    });
                    log.debug(`Last checked: ${timeSinceLastCheck} minutes ago`, {
                        launchId: launch.id,
                        timeSinceLastCheck
                    });

                    if (timeSinceLastCheck >= 5 && timeSinceLastApiCall >= config.api.rateLimitMinutes) {
                        log.info(`Refreshing data for imminent launch: ${launch.name}`, { launchId: launch.id });

                        // Do a direct launch lookup
                        const latestLaunch = await getLatestLaunchData(launch.id);
                        if (latestLaunch) {
                            // Update the launch data in our cache
                            const index = state.launchesCache.data.findIndex(l => l.id === launch.id);
                            if (index !== -1) {
                                state.launchesCache.data[index] = latestLaunch;
                            }

                            // Update what we're checking
                            launches[i] = latestLaunch;

                            // Update tracking
                            state.imminentLaunches.set(launch.id, moment());
                            state.lastApiCallTime = moment();

                            // Check for launch delays
                            const delayInfo = checkLaunchDelay(launch.id, latestLaunch.net, state.launchTimes);
                            if (delayInfo && !state.delayNotifiedLaunches.has(launch.id)) {
                                log.warn(`LAUNCH DELAY DETECTED: ${launch.name} delayed by ${delayInfo.delayMinutes} minutes`, {
                                    launchId: launch.id,
                                    delayMinutes: delayInfo.delayMinutes,
                                    originalTime: delayInfo.originalTime,
                                    newTime: delayInfo.newTime
                                });

                                // Send delay notification
                                const notificationSent = await sendLaunchDelayNotification(latestLaunch, delayInfo);

                                if (notificationSent) {
                                    // Mark as notified for delay
                                    state.delayNotifiedLaunches.add(launch.id);
                                    log.info(`Delay notification sent for ${launch.name}`, { launchId: launch.id });

                                    // If we previously notified about this launch, remove it so we can notify again at the new time
                                    if (state.notifiedLaunches.has(launch.id)) {
                                        state.notifiedLaunches.delete(launch.id);
                                        log.info(`Removed ${launch.name} from notified launches to allow re-notification at new time`, { launchId: launch.id });
                                    }
                                }
                            }

                            // Log if webcast status changed
                            const oldHasWebcast = hasLivestream(launch);
                            const newHasWebcast = hasLivestream(latestLaunch);

                            if (!oldHasWebcast && newHasWebcast) {
                                log.info(`🔴 WEBCAST DETECTED! Launch ${launch.name} now has a webcast!`, {
                                    launchId: launch.id,
                                    webcastUrls: getLivestreamUrls(latestLaunch)
                                });
                            }

                            // If in debug mode, log image fields of the updated launch
                            if (config.debug) {
                                imageProvider.logLaunchImageFields(latestLaunch);
                            }
                        }
                    }
                }
            }

            // Log upcoming launches with their exact times
            if (config.debug) {
                launches.forEach(launch => {
                    const launchTime = moment(launch.net);
                    const minutesUntilLaunch = Math.round(launchTime.diff(moment(), 'minutes', true));
                    const hasStream = hasLivestream(launch);
                    log.debug(`- ${launch.name}: T-${minutesUntilLaunch}m (${launchTime.format('HH:mm:ss')}) | Stream: ${hasStream ? 'YES' : 'NO'}`, {
                        launchId: launch.id,
                        launchTime: launchTime.format(),
                        minutesUntilLaunch,
                        hasStream
                    });

                    if (hasStream) {
                        const urls = getLivestreamUrls(launch);
                        log.debug(`  Webcast URLs: ${Array.isArray(urls) ? urls.join(', ') : JSON.stringify(urls)}`, {
                            launchId: launch.id,
                            webcastUrls: urls
                        });
                        log.debug(`  Webcast live: ${launch.webcast_live}`, {
                            launchId: launch.id,
                            webcastLive: launch.webcast_live
                        });

                        // Debug the vidURLs structure
                        if (launch.vidURLs) {
                            log.debug(`  vidURLs type: ${Array.isArray(launch.vidURLs) ? 'Array' : typeof launch.vidURLs}`, {
                                launchId: launch.id,
                                vidURLsType: Array.isArray(launch.vidURLs) ? 'Array' : typeof launch.vidURLs
                            });
                            if (typeof launch.vidURLs === 'object' && !Array.isArray(launch.vidURLs)) {
                                log.debug(`  vidURLs keys: ${Object.keys(launch.vidURLs).join(', ')}`, {
                                    launchId: launch.id,
                                    vidURLsKeys: Object.keys(launch.vidURLs)
                                });
                            }
                        }
                    }
                });
            }

            let notificationSent = false;

            // Check for launches that need notifications
            for (const launch of launches) {
                if (isUpcomingLaunchWithStream(launch, now)) {
                    log.info(`Found launch within exact ${config.notifications.defaultTime}-minute notification window: ${launch.name}`, {
                        launchId: launch.id,
                        launchName: launch.name,
                        notificationWindow: config.notifications.defaultTime
                    });
                    log.info(`Preparing launch notification with image for ${launch.name}`, { launchId: launch.id });

                    // Examine the image fields available for this launch if in debug mode
                    if (config.debug) {
                        imageProvider.logLaunchImageFields(launch);
                    }

                    // Use the two-part notification approach
                    const success = await sendLaunchNotificationWithImage(launch);

                    if (success) {
                        notificationSent = true;
                        log.info(`Successfully sent notification for ${launch.name}`, { launchId: launch.id });

                        // Add to notified set
                        state.notifiedLaunches.add(launch.id);

                        // If no webcast URL is available, schedule a check for it at T-1 minute
                        const livestreamUrls = getLivestreamUrls(launch);
                        if (livestreamUrls.length === 0) {
                            const launchTime = moment(launch.net);
                            const checkTime = launchTime.subtract(1, 'minutes').toDate();
                            const { scheduleUpdate, checkWebcastUpdate } = require('./utils/update-handler');
                            scheduleUpdate(launch.id, checkTime, checkWebcastUpdate);
                        }

                        // Clean up notified set (keep it from growing too large)
                        if (state.notifiedLaunches.size > 100) {
                            const iterator = state.notifiedLaunches.values();
                            state.notifiedLaunches.delete(iterator.next().value);
                        }
                    } else {
                        log.error(`Failed to send notification for ${launch.name}`, { launchId: launch.id });

                        // Try fallback to just text message if two-part notification failed
                        try {
                            log.info(`Attempting fallback to text-only notification for ${launch.name}`, { launchId: launch.id });
                            const textMessage = await messageTemplates.formatLaunchNotification(launch);
                            const textSuccess = await sendGroupMessage(textMessage);

                            if (textSuccess) {
                                notificationSent = true;
                                log.info(`Successfully sent fallback text notification for ${launch.name}`, { launchId: launch.id });

                                // Add to notified set
                                state.notifiedLaunches.add(launch.id);
                            } else {
                                log.error(`Failed to send even fallback notification for ${launch.name}`, { launchId: launch.id });
                            }
                        } catch (fallbackError) {
                            log.error(`Error in fallback notification for ${launch.name}`, {
                                launchId: launch.id,
                                error: fallbackError.message,
                                stack: fallbackError.stack
                            });
                        }
                    }
                }
            }

            if (!notificationSent && launches.length > 0) {
                log.debug('No launches requiring notification at this time', { launchCount: launches.length });
            }
        } catch (error) {
            log.error('Error in launch monitor', { error: error.message, stack: error.stack });
        }
    });

    // Add to scheduled jobs
    state.scheduledJobs.push(job);

    // Also add a daily cleanup job to clear old cache entries at midnight
    const cleanupJob = schedule.scheduleJob('0 0 * * *', () => {
        log.info('Performing daily cache cleanup');
        // Clear notified launches
        state.notifiedLaunches.clear();
        // Clear delay notified launches
        state.delayNotifiedLaunches.clear();
        // Reset launch times tracking to avoid stale data
        state.launchTimes.clear();
        // Force cache refresh
        state.launchesCache.timestamp = null;
        // Clear image cache if it's getting too large (over 100 entries)
        if (state.imageCache && state.imageCache.size > 100) {
            const cleared = imageProvider.clearImageCache();
            log.info(`Cleared ${cleared} entries from image cache`, { clearedEntries: cleared });
        }
    });

    state.scheduledJobs.push(cleanupJob);

    return job;
}

  return {
    startLaunchMonitor,
    isUpcomingLaunchWithStream
  };
};