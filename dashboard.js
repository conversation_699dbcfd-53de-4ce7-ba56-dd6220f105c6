// dashboard.js - Health dashboard for Space Launch Notifier
    module.exports = (config, state) => {
    const express = require('express');
    const http = require('http');
    const socketIo = require('socket.io');
    const fs = require('fs');
    const path = require('path');
    const moment = require('moment');

    // Import other modules we'll need for stats and functionality
    const connectionMonitor = require('./utils/connection-monitor');
    const urlTracker = require('./utils/url-tracker');
    const api = require('./utils/api');
    const { fetchUpcomingLaunches, fetchLaunchesForPeriod } = api;
    const messaging = require('./utils/messaging');
    const helpers = require('./utils/helpers');
    const schedule = require('node-schedule');
    const spacePeople = require('./utils/space-people');
    const dailyDigestModule = require('./daily-digest');

    // Import additional modules for message templates
    const languageManager = require('./utils/language-manager');
    const defaultConfig = require('./utils/default-config');
    const i18nLoader = require('./utils/i18n/loader');

    // Initialize message templates
    const messageTemplates = require('./utils/message-templates')(config, {
        moment,
        hasLivestream: helpers.hasLivestream,
        getLivestreamUrls: helpers.getLivestreamUrls,
        languageManager,
        defaultConfig,
        i18nLoader
    });

    let logger;
    let log;
    try {
        logger = require('./utils/logger');
        log = logger.getLogger('dashboard');
    } catch (e) {
        // Fallback to console logging if logger is not available
        console.log('Logger not available, using console fallback');
        logger = null;
        log = {
            error: (message, meta = {}) => console.error(message, meta),
            warn: (message, meta = {}) => console.warn(message, meta),
            info: (message, meta = {}) => console.log(message, meta),
            debug: (message, meta = {}) => (console.debug || console.log)(message, meta),
            http: (message, meta = {}) => console.log(message, meta)
        };
    }

    // Initialize daily digest module with all its dependencies
    const dailyDigest = dailyDigestModule(config, state, {
        messageTemplates,
        logger,
        api,
        messaging,
        helpers,
        connectionMonitor,
        urlTracker,
        moment,
        schedule,
        spacePeople
    });

    // Log streaming
    const logStreams = new Map(); // Map to track active log streams

    /**
     * Parse log content into structured log lines
     * @param {string} content - Raw log content
     * @returns {Array} Array of parsed log lines
     */
    function parseLogLines(content) {
        return content.split('\n')
            .filter(line => line.trim())
            .map(line => {
                try {
                    // Extract timestamp, level, and message
                    const match = line.match(/^(\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}) (\w+): (?:\[(.*?)\] )?(.*)$/);
                    if (match) {
                        const [, timestamp, level, module, message] = match;
                        return { timestamp, level, module: module || '', message };
                    }
                    return { raw: line };
                } catch (e) {
                    return { raw: line };
                }
            });
    }

    // Create Express app and HTTP server
    const app = express();
    const server = http.createServer(app);
    const io = socketIo(server, {
        cors: {
            origin: "https://sln-dev.molarmanor.co.uk",
            methods: ["GET", "POST"]
        }
    });

    // Create dashboard directory if it doesn't exist
    const DASHBOARD_DIR = path.join(__dirname, 'dashboard');
    if (!fs.existsSync(DASHBOARD_DIR)) {
        log.info('Creating dashboard directory');
        fs.mkdirSync(DASHBOARD_DIR, { recursive: true });
    }

    // Dashboard data
    let dashboardStats = {
        health: {
            status: 'unknown',
            lastCheck: null,
            uptime: 0,
            startTime: Date.now(),
            whatsappConnected: false,
            dashboardPort: 3030
        },
        messages: {
            sent: 0,
            failed: 0,
            lastSent: null,
            lastContent: null
        },
        urls: {
            total: 0,
            images: 0,
            launches: 0
        },
        launches: {
            upcoming: 0,
            notified: 0,
            withWebcast: 0
        }
    };

    // Update dashboard stats
    function updateStats() {
        // Update health stats
        dashboardStats.health.lastCheck = Date.now();
        dashboardStats.health.uptime = Math.floor((Date.now() - dashboardStats.health.startTime) / 1000);
        dashboardStats.health.whatsappConnected = !!state.whatsappClient;
        dashboardStats.health.status = dashboardStats.health.whatsappConnected ? 'healthy' : 'disconnected';

        // Update URL stats if URL tracker is available
        if (urlTracker && typeof urlTracker.getTrackingStats === 'function') {
            const urlStats = urlTracker.getTrackingStats();
            dashboardStats.urls = urlStats;
        }

        // Update launch stats
        if (state.launchesCache && state.launchesCache.data) {
            dashboardStats.launches.upcoming = state.launchesCache.data.length;
            dashboardStats.launches.notified = state.notifiedLaunches.size;

            // Count launches with webcasts
            dashboardStats.launches.withWebcast = state.launchesCache.data.filter(launch => {
                return launch.webcast_live ||
                    (launch.vidURLs && launch.vidURLs.length > 0) ||
                    (launch.webcast_url && launch.webcast_url.length > 0);
            }).length;
        }

        // Emit updated stats to all connected clients
        io.emit('stats-update', dashboardStats);
    }

    // Set up Socket.IO connection
    io.on('connection', (socket) => {
        log.info('Dashboard client connected', {
            clientId: socket.id,
            clientIP: socket.handshake.address
        });

        // Send initial stats to the new client
        socket.emit('stats-update', dashboardStats);

        // Handle client disconnect
        socket.on('disconnect', () => {
            log.info('Dashboard client disconnected', { clientId: socket.id });

            // Clean up any active log streams for this socket
            if (logStreams.has(socket.id)) {
                const { watcher, file } = logStreams.get(socket.id);
                if (watcher) {
                    watcher.close();
                    log.debug(`Closed log stream for ${file}`, { clientId: socket.id });
                }
                logStreams.delete(socket.id);
            }
        });

        // Handle manual refresh request
        socket.on('refresh-stats', () => {
            log.debug('Manual stats refresh requested');
            updateStats();
        });

        // Handle log streaming requests
        socket.on('start-log-stream', async (fileName) => {
            if (!logger) {
                socket.emit('log-stream-error', { error: 'Logging system not available' });
                return;
            }

            try {
                // Clean up any existing stream for this socket
                if (logStreams.has(socket.id)) {
                    const { watcher } = logStreams.get(socket.id);
                    if (watcher) watcher.close();
                }

                const filePath = path.join(logger.getLogDir(), fileName);

                // Check if file exists
                if (!fs.existsSync(filePath)) {
                    socket.emit('log-stream-error', { error: `Log file ${fileName} not found` });
                    return;
                }

                log.debug(`Starting log stream for ${fileName}`, { clientId: socket.id });

                // Send initial content
                const initialContent = await logger.readLogFile(fileName);
                const initialLines = parseLogLines(initialContent);
                socket.emit('log-stream-init', { lines: initialLines });

                let lastReadPosition = initialContent.length; // Track the last read position

                // Set up file watcher for real-time updates
                const watcher = fs.watch(filePath, async (eventType) => {
                    if (eventType === 'change') {
                        try {
                            const stats = await fs.promises.stat(filePath);
                            const currentFileSize = stats.size;

                            if (currentFileSize < lastReadPosition) {
                                // File has been truncated or rotated, restart from beginning
                                log.debug(`Log file ${fileName} truncated or rotated. Restarting stream.`, { clientId: socket.id });
                                lastReadPosition = 0;
                                socket.emit('log-stream-init', { lines: [] }); // Clear existing logs on client
                            }

                            const readStream = fs.createReadStream(filePath, { start: lastReadPosition, encoding: 'utf8' });
                            let newContent = '';

                            readStream.on('data', (chunk) => {
                                newContent += chunk;
                            });

                            readStream.on('end', () => {
                                if (newContent.length > 0) {
                                    const newLines = parseLogLines(newContent);
                                    if (newLines.length > 0) {
                                        socket.emit('log-stream-update', { lines: newLines });
                                    }
                                    lastReadPosition += Buffer.byteLength(newContent, 'utf8');
                                }
                            });

                            readStream.on('error', (error) => {
                                log.error(`Error reading log stream update for ${fileName}: ${error.message}`, { error, clientId: socket.id });
                                socket.emit('log-stream-error', { error: `Error reading stream: ${error.message}` });
                                watcher.close(); // Close watcher on error
                                logStreams.delete(socket.id);
                            });

                        } catch (error) {
                            log.error(`Error processing log file change for ${fileName}: ${error.message}`, { error, clientId: socket.id });
                            socket.emit('log-stream-error', { error: `Error processing change: ${error.message}` });
                            watcher.close(); // Close watcher on error
                            logStreams.delete(socket.id);
                        }
                    }
                });

                // Store the watcher reference
                logStreams.set(socket.id, { watcher, file: fileName });

                // Handle stop streaming request
                socket.on('stop-log-stream', () => {
                    if (logStreams.has(socket.id)) {
                        const { watcher } = logStreams.get(socket.id);
                        if (watcher) watcher.close();
                        logStreams.delete(socket.id);
                        log.debug(`Stopped log stream for ${fileName}`, { clientId: socket.id });
                    }
                });

            } catch (error) {
                log.error(`Error setting up log stream: ${error.message}`, { error });
                socket.emit('log-stream-error', { error: `Failed to stream log: ${error.message}` });
            }
        });
    });

    // Create API routes
    app.get('/api/health', (req, res) => {
        log.info('Received request for /api/health');
        updateStats();
        res.json(dashboardStats.health);
    });

    // Reconnect WhatsApp endpoint
    app.post('/api/reconnect', async (req, res) => {
        log.info('Manual reconnection requested from dashboard', {
            ip: req.ip,
            userAgent: req.get('User-Agent')
        });

        try {
            // Check if connection monitor is available
            if (connectionMonitor && typeof connectionMonitor.attemptReconnect === 'function') {
                // Update status
                dashboardStats.health.status = 'reconnecting';
                io.emit('status-update', { status: 'reconnecting' });

                // Attempt reconnection
                await connectionMonitor.attemptReconnect();
                log.info('Manual reconnection initiated successfully');

                // Update stats after reconnection attempt
                updateStats();
                res.json({ success: true, message: 'Reconnection attempt initiated' });
            } else {
                log.error('Connection monitor not available for reconnection');
                res.status(500).json({ success: false, message: 'Connection monitor not available' });
            }
        } catch (error) {
            log.error('Error during manual reconnection', {
                error: error.message,
                stack: error.stack
            });
            res.status(500).json({ success: false, message: 'Reconnection failed: ' + error.message });
        }
    });

    app.get('/api/messages', (req, res) => {
        res.json(dashboardStats.messages);
    });

    app.get('/api/last-message', (req, res) => {
        if (dashboardStats.messages.lastContent) {
            res.json({ message: dashboardStats.messages.lastContent });
        } else {
            res.json({ message: null });
        }
    });

    app.get('/api/urls', (req, res) => {
        if (urlTracker && typeof urlTracker.getTrackingStats === 'function') {
            dashboardStats.urls = urlTracker.getTrackingStats();
        }
        res.json(dashboardStats.urls);
    });

    app.get('/api/launch-stats', (req, res) => {
        res.json(dashboardStats.launches);
    });

    // Get detailed launch data for the Launches tab
    app.get('/api/launches', async (req, res) => {
        try {
            // Check if we have cached launches
            if (state.launchesCache && state.launchesCache.data && state.launchesCache.data.length > 0) {
                log.info('Returning cached launches for dashboard', {
                    cacheSize: state.launchesCache.data.length,
                    cacheAge: state.launchesCache.timestamp ?
                        Math.floor((Date.now() - state.launchesCache.timestamp) / 1000) : 'unknown'
                });
                return res.json(state.launchesCache.data);
            }

            // Fetch fresh data if no cache
            log.info('Fetching fresh launch data for dashboard');
            const launches = await fetchUpcomingLaunches();
            return res.json(launches);
        } catch (error) {
            log.error('Error fetching launches for dashboard', {
                error: error.message,
                stack: error.stack
            });
            return res.status(500).json({ error: 'Failed to fetch launch data' });
        }
    });

    // Get daily digest message
    app.get('/api/daily-digest', async (req, res) => {
        log.info('Received request for /api/daily-digest');
        try {
            log.info('Generating daily digest preview for dashboard');

            // Get current date and calculate end date
            const now = moment();
            const endDate = moment(now).add(config.dailyDigest.lookAheadDays, 'days');

            // Fetch launches for the period
            const launches = await fetchLaunchesForPeriod(now, endDate);

            if (!launches || launches.length === 0) {
                log.info('No launches found for the digest period', {
                    startDate: now.format(),
                    endDate: endDate.format(),
                    lookAheadDays: config.dailyDigest.lookAheadDays
                });
                // Use status message template for the empty digest
                const message = messageTemplates.formatStatusMessage('empty-digest', {
                    date: now.format('MMMM D, YYYY')
                });

                return res.json({ message });
            }

            // Generate digest message
            const digestMessage = await messageTemplates.formatDailyDigest(launches, now, endDate, config);

            return res.json({ message: digestMessage });
        } catch (error) {
            log.error('Error generating daily digest for dashboard', {
                error: error.message,
                stack: error.stack
            });
            return res.status(500).json({ error: 'Failed to generate daily digest' });
        }
    });

    app.get('/api/config', (req, res) => {
        // Return a safe subset of the configuration
        const safeConfig = {
            whatsapp: {
                sessionName: config.whatsapp.sessionName,
                connectionRetry: config.whatsapp.connectionRetry
            },
            api: {
                cacheDuration: config.api.cacheDuration,
                maxResults: config.api.maxResults,
                rateLimitMinutes: config.api.rateLimitMinutes
            },
            notifications: config.notifications,
            dailyDigest: config.dailyDigest,
            language: config.language,
            images: {
                enabled: config.images.enabled,
                useHighRes: config.images.useHighRes,
                preferMissionPatches: config.images.preferMissionPatches,
                fallbackToGeneric: config.images.fallbackToGeneric,
                cacheImages: config.images.cacheImages
            },
            urlTracking: {
                enabled: config.urlTracking.enabled,
                shortenUrls: config.urlTracking.shortenUrls,
                trackClicks: config.urlTracking.trackClicks
            },
            debug: config.debug
        };

        res.json(safeConfig);
    });

    app.get('/api/languages', (req, res) => {
        // Get language information
        const languages = require('./utils/i18n/config').SUPPORTED_LANGUAGES;
        res.json(languages);
    });

    app.get('/api/version', (req, res) => {
        const pjson = require('./package.json');
        res.json({ version: pjson.version });
    });

    // New endpoint to get WhatsApp Group ID
    app.get('/api/whatsapp-group-id', (req, res) => {
        res.json({ whatsappGroupId: config.whatsapp.groupId });
    });

    // Log API endpoints
    app.get('/api/logs', async (req, res) => {
        if (!logger) {
            return res.status(404).json({ error: 'Logging system not available' });
        }

        try {
            const logFiles = await logger.getLogFiles();
            res.json(logFiles);
        } catch (error) {
            log.error('Error getting log files', { error: error.message, stack: error.stack });
            res.status(500).json({ error: 'Failed to get log files' });
        }
    });

    app.get('/api/logs/:filename', async (req, res) => {
        if (!logger) {
            return res.status(404).json({ error: 'Logging system not available' });
        }

        try {
            const filename = req.params.filename;
            const logContent = await logger.readLogFile(filename);

            // Check if the client wants JSON or plain text
            const format = req.query.format || 'text';

            if (format === 'json') {
                // Parse log content into JSON array using our helper function
                const logLines = parseLogLines(logContent);

                // Apply filters if provided
                const level = req.query.level;
                const search = req.query.search;

                let filteredLines = logLines;

                if (level && level !== 'all') {
                    filteredLines = filteredLines.filter(line => !line.level || line.level === level);
                }

                if (search) {
                    const searchLower = search.toLowerCase();
                    filteredLines = filteredLines.filter(line => {
                        if (line.raw) return line.raw.toLowerCase().includes(searchLower);
                        return (
                            (line.message && line.message.toLowerCase().includes(searchLower)) ||
                            (line.module && line.module.toLowerCase().includes(searchLower))
                        );
                    });
                }

                res.json(filteredLines);
            } else {
                // Return plain text
                res.type('text/plain').send(logContent);
            }
        } catch (error) {
            log.error(`Error reading log file ${req.params.filename}:`, error);
            res.status(500).json({ error: 'Failed to read log file' });
        }
    });

    // Get the latest logs (tail functionality)
    app.get('/api/logs/:filename/tail', async (req, res) => {
        if (!logger) {
            return res.status(404).json({ error: 'Logging system not available' });
        }

        try {
            const filename = req.params.filename;
            const filePath = path.join(logger.getLogDir(), filename);

            // Check if file exists
            if (!fs.existsSync(filePath)) {
                return res.status(404).json({ error: `Log file ${filename} not found` });
            }

            // Get file size
            const stats = await fs.promises.stat(filePath);
            const fileSize = stats.size;

            // Determine how many bytes to read from the end
            const linesToRead = parseInt(req.query.lines) || 50;
            // Estimate bytes per line (average) and add a buffer
            const estimatedBytes = linesToRead * 200 + 1024; // 200 bytes/line + 1KB buffer
            const startByte = Math.max(0, fileSize - estimatedBytes);

            const stream = fs.createReadStream(filePath, { start: startByte, encoding: 'utf8' });
            let content = '';
            let lastLineBreak = -1;

            stream.on('data', (chunk) => {
                content += chunk;
            });

            stream.on('end', () => {
                // Find the last newline character to ensure we start from a complete line
                lastLineBreak = content.indexOf('\n');
                if (lastLineBreak !== -1) {
                    content = content.substring(lastLineBreak + 1);
                }

                const allLines = content.split('\n').filter(line => line.trim());
                const tailLines = allLines.slice(-linesToRead);

                // Parse and filter lines (same logic as before)
                const parsedLines = tailLines.map(line => {
                    try {
                        const match = line.match(/^(\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}) (\w+): (?:\[(.*?)\] )?(.*)$/);
                        if (match) {
                            const [, timestamp, level, module, message] = match;
                            return { timestamp, level, module: module || '', message };
                        }
                        return { raw: line };
                    } catch (e) {
                        return { raw: line };
                    }
                });

                const levelFilter = req.query.level;
                const searchFilter = req.query.search;

                let filteredLines = parsedLines;

                if (levelFilter && levelFilter !== 'all') {
                    filteredLines = filteredLines.filter(line => !line.level || line.level === levelFilter);
                }

                if (searchFilter) {
                    const searchLower = searchFilter.toLowerCase();
                    filteredLines = filteredLines.filter(line => {
                        if (line.raw) return line.raw.toLowerCase().includes(searchLower);
                        return (
                            (line.message && line.message.toLowerCase().includes(searchLower)) ||
                            (line.module && line.module.toLowerCase().includes(searchLower))
                        );
                    });
                }

                res.json(filteredLines);
            });

            stream.on('error', (error) => {
                log.error(`Error reading log file stream ${filename}:`, error);
                res.status(500).json({ error: 'Failed to read log file stream' });
            });

        } catch (error) {
            log.error(`Error tailing log file ${req.params.filename}:`, error);
            res.status(500).json({ error: 'Failed to tail log file' });
        }
    });

    // New endpoint to clear logs
    app.post('/api/logs/clear', async (req, res) => {
        if (!logger || typeof logger.clearLogs !== 'function') {
            return res.status(404).json({ error: 'Log clearing not available' });
        }

        try {
            const success = await logger.clearLogs();
            if (success) {
                log.info('Logs cleared from dashboard.');
                res.json({ success: true, message: 'Logs cleared successfully' });
            } else {
                res.status(500).json({ success: false, message: 'Failed to clear logs' });
            }
        } catch (error) {
            log.error('Error clearing logs from dashboard:', error);
            res.status(500).json({ success: false, message: 'Error clearing logs' });
        }
    });

    // Serve static files from the dashboard directory

    app.use(express.static(path.join(__dirname, 'dashboard')));

    // Main dashboard page
    app.get('/', (req, res) => {
        res.sendFile(path.join(__dirname, 'dashboard', 'index.html'));
    });

    // Add test endpoints if in debug mode
    if (config.debug) {
        log.info('Debug mode is enabled. Adding test endpoints.');

        // Middleware to check for debug environment
        const isDebugEnvironment = (req, res, next) => {
            if (config.debug) {
                next();
            } else {
                log.warn(`Test endpoint accessed when debug mode is off.`);
                res.status(403).send('Forbidden: Test endpoints are only available in debug mode.');
            }
        };

        app.post('/test/daily-digest', isDebugEnvironment, async (req, res) => {
            log.info('Received request to test daily digest');
            try {
                const success = await dailyDigest.sendDailyDigest();
                if (success) {
                    res.status(200).json({ success: true, message: 'Daily digest sent successfully.' });
                } else {
                    res.status(500).json({ success: false, message: 'Failed to send daily digest.' });
                }
            } catch (error) {
                log.error('Error sending test daily digest', { error: error.message, stack: error.stack });
                res.status(500).json({ success: false, message: 'An error occurred while sending the daily digest.' });
            }
        });

        app.post('/test/launch-notification', isDebugEnvironment, async (req, res) => {
            log.info('Received request to test launch notification');
            try {
                // Create a mock launch object for testing
                const mockLaunch = {
                    id: 'test-launch-123',
                    name: 'Test Launch | Gemini Mission',
                    net: new Date().toISOString(),
                    status: { name: 'Go for Launch' },
                    launch_service_provider: { name: 'Gemini Space Program' },
                    rocket: { configuration: { full_name: 'Gemini Titan IV' } },
                    mission: { name: 'Test Mission', description: 'This is a test launch notification from the Gemini CLI.' },
                    pad: { name: 'Test Pad 1', location: { name: 'Cape Gemini' } },
                    webcast_live: true,
                    vidURLs: [{ url: 'https://www.youtube.com/watch?v=gemini-test' }],
                    image: 'https://www.nasa.gov/wp-content/uploads/2023/03/nasa-logo-web-rgb.png'
                };

                const success = await messaging.sendLaunchNotificationWithImage(mockLaunch);

                if (success) {
                    res.status(200).json({ success: true, message: 'Test launch notification sent successfully.' });
                } else {
                    res.status(500).json({ success: false, message: 'Failed to send test launch notification.' });
                }
            } catch (error) {
                log.error('Error sending test launch notification', { error: error.message, stack: error.stack });
                res.status(500).json({ success: false, message: 'An error occurred while sending the test launch notification.' });
            }
        });
    }

    // Start the dashboard server
    function startDashboard(port = process.env.DASHBOARD_PORT || 3030, maxRetries = 5) {
        // Update stats every 10 seconds
        setInterval(updateStats, 10000);

        // Try to start the server with port fallback
        return new Promise((resolve, reject) => {
            let currentPort = port;
            let retries = 0;

            const tryListen = () => {
                // Handle errors when binding to the port
                server.on('error', (err) => {
                    if (err.code === 'EADDRINUSE') {
                        log.warn(`Port ${currentPort} is already in use, trying next port...`);
                        server.close();

                        // Try the next port if we haven't exceeded max retries
                        if (retries < maxRetries) {
                            retries++;
                            currentPort++;
                            tryListen();
                        } else {
                            log.error(`Failed to find an available port after ${maxRetries} attempts`, {
                                startPort: port,
                                lastTriedPort: currentPort,
                                error: err.message
                            });
                            reject(err);
                        }
                    } else {
                        log.error('Dashboard server error', {
                            error: err.message,
                            code: err.code,
                            stack: err.stack
                        });
                        reject(err);
                    }
                });

                // Try to listen on the current port
                server.listen(currentPort, () => {
                    log.info(`Dashboard running at http://localhost:${currentPort}`);

                    // Log additional information in debug mode
                    if (config.debug) {
                        log.debug('Dashboard server details', {
                            port: currentPort,
                            environment: logger && logger.isHomeAssistant ?
                                (logger.isHomeAssistant() ? 'Home Assistant' : 'Standalone') : 'Unknown',
                            nodeVersion: process.version
                        });
                    }

                    // Update the port in the dashboard stats
                    dashboardStats.health.dashboardPort = currentPort;
                    resolve({ server, port: currentPort });
                });
            };

            // Start trying to listen
            tryListen();
        });
    }

    // Record a message being sent
    function recordMessageSent(success = true, messageContent = null) {
        if (success) {
            dashboardStats.messages.sent++;
            dashboardStats.messages.lastSent = Date.now();

            // Store the message content if provided
            if (messageContent) {
                dashboardStats.messages.lastContent = messageContent;
            }
        } else {
            dashboardStats.messages.failed++;
        }
    }

    module.exports = {
        startDashboard,
        recordMessageSent
    };
};
