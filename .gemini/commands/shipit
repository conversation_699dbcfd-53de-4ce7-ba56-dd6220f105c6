# shipit.toml
# This file defines a custom Gemini CLI command to run the 'shipit.sh' script.

# Define the 'shipit' command
[commands.shipit]
description = "Executes the 'Ship It' process to prepare a release for either 'dev' or 'prod' environments."
# The command to execute. This assumes shipit.sh is in the project root or accessible via PATH.
# If shipit.sh is in a specific subdirectory, adjust the path here (e.g., "./scripts/shipit.sh").
shell = "./scripts/shipit.sh"

# Define the parameters that the 'shipit.sh' script expects.
# <PERSON> will prompt the user for these values if they are not provided on the command line.
[[commands.shipit.parameters]]
name = "ENV"
description = "The target environment for the release (dev or prod)."
type = "string"
# You can add validation here if desired, e.g., to restrict to "dev" or "prod"
pattern = "^(dev|prod)$"
# error_message = "Environment must be 'dev' or 'prod'."
required = true
