#!/bin/bash

echo "Starting run.sh script..."

# Force non-interactive mode for logging
export CI=true

# Create necessary directories
mkdir -p /data/tokens /data/logs

# Set Home Assistant environment flag
export HOMEASSISTANT_CONTAINER=true

# Set dashboard port for ingress
export DASHBOARD_PORT=3030

echo "Attempting to start Node.js application..."

# Start the application
node index.js &
NODE_PID=$!

# Wait for the Node.js process to exit
wait ${NODE_PID}
NODE_EXIT_CODE=$?

echo "Node.js application exited with code: ${NODE_EXIT_CODE}"