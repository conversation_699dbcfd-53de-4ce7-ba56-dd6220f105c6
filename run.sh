#!/usr/bin/with-contenv bashio

# Ensure bashio is sourced
if ! command -v bashio::log.info &> /dev/null; then
    echo "Error: bashio is not properly sourced or available!"
    exit 1
fi

bashio::log.info "Starting run.sh script..."

# Force non-interactive mode for logging
export CI=true

# Create necessary directories
mkdir -p /data/tokens /data/logs

# Set environment variables from Home Assistant options
# These are now directly set in config.yaml under the 'environment' key

# Set Home Assistant environment flag
export HOMEASSISTANT_CONTAINER=true

# Set dashboard port for ingress
export DASHBOARD_PORT=3030

bashio::log.info "Attempting to start Node.js application..."

# Start the application
node start.js &
NODE_PID=$!

# Wait for the Node.js process to exit
wait ${NODE_PID}
NODE_EXIT_CODE=$?

bashio::log.info "Node.js application exited with code: ${NODE_EXIT_CODE}"

# Temporarily remove bashio::exit_code to test other bashio functions
# bashio::exit_code ${NODE_EXIT_CODE}